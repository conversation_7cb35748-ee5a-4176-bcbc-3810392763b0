import { useState, useEffect } from 'react';

interface SubscriptionStatus {
  subscription_status: string | null;
  plan_name: string | null;
  subscription_start: Date | null;
  subscription_end: Date | null;
  billing_cycle: string | null;
  trial_start: Date | null;
  trial_end: Date | null;
  plan_id: string | null;
}

export const useSubscriptionStatus = () => {
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [showRenewalBanner, setShowRenewalBanner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const refreshSubscriptionStatus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/subscription/status');
      if (!response.ok) {
        throw new Error('Failed to fetch subscription status');
      }
      const data = await response.json();
      setSubscriptionStatus(data);
      // Include 'incomplete' as active status since payment may have succeeded
      const activeStatuses = ['active', 'trialing', 'cancel_at_period_end', 'incomplete'];
      setHasActiveSubscription(activeStatuses.includes(data.subscription_status ?? ''));
    } catch (error) {
      console.error('Error fetching subscription status:', error);
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    refreshSubscriptionStatus();
  }, []);

  return {
    subscriptionStatus,
    hasActiveSubscription,
    showRenewalBanner,
    setShowRenewalBanner,
    refreshSubscriptionStatus,
    isLoading
  };
};
