import { ChatMessage, convertTo<PERSON><PERSON> } from './types';
import { supabase } from '@/lib/supabase/client';

// Save chat history to user's content
export const saveChatHistory = async (userId: string, updatedMessages: ChatMessage[]): Promise<boolean> => {
  try {
    // Convert ChatMessage[] to a format compatible with Supabase JSON storage
    const safeMessages = convertToJson(updatedMessages);

    const { data, error } = await supabase
      .from('user_content')
      .select('id')
      .eq('user_id', userId)
      .eq('type', 'chat')
      .single();
      
    if (error && error.code !== 'PGRST116') {
      console.error('Error checking for existing chat history:', error);
      return false;
    }
    
    // Update or insert logic
    if (data) {
      // Update existing record
      const { error: updateError } = await supabase
        .from('user_content')
        .update({ content_metadata: { messages: safeMessages } })
        .eq('id', data.id);
      
      if (updateError) {
        console.error('Error updating chat history:', updateError);
        return false;
      }
    } else {
      // Insert new record
      const { error: insertError } = await supabase
        .from('user_content')
        .insert({
          user_id: userId,
          type: 'chat',
          title: 'Chat History',
          content_metadata: { messages: safeMessages }
        });
      
      if (insertError) {
        console.error('Error inserting chat history:', insertError);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error saving chat history:', error);
    return false;
  }
};
