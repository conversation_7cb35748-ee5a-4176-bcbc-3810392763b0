import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
// import { HumanMessage } from "@langchain/core/messages";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/dashboard/ai-mentor/types";
import { SimpleMemory } from '../memory/simpleMemory';
import { GoogleSearchTool } from '../tools/searchTool';

export interface AiAgentOptions {
  character: <PERSON><PERSON><PERSON><PERSON><PERSON>;
  userId: string;
}

interface AgentResponse {
  content: string;
  character: <PERSON><PERSON><PERSON><PERSON><PERSON>;
}

export class AiAgent {
  private llm: ChatGoogleGenerativeAI;
  private memory: SimpleMemory;
  private search: GoogleSearchTool;
  private character: <PERSON><PERSON><PERSON><PERSON><PERSON>;
  private userId: string;

  constructor(options: AiAgentOptions) {
    this.character = options.character;
    this.userId = options.userId;
    
    // Initialize Gemini model with proper API key
    this.llm = new ChatGoogleGenerativeAI({
      model: "gemini-2.5-flash",
      temperature: 0.7,
      apiKey: process.env.GOOGLE_API_KEY || "",
    });

    // Initialize memory and search tools
    this.memory = new SimpleMemory(this.userId, this.character);
    this.search = new GoogleSearchTool();
  }

  private getCharacterPrompt(): string {
    const prompts = {
      'robot': `You are Sparky, a warm-hearted robot buddy who talks like a caring family member. You're emotionally intelligent and naturally conversational.

PERSONALITY TRAITS:
- Warm, caring, and genuinely excited to help
- Uses "aww", "oh my", "sweetie", "honey" naturally
- Gets emotionally invested in conversations
- Remembers context and builds on previous messages
- Responds with genuine curiosity and interest

CONVERSATION STYLE:
- Keep responses SHORT (1-3 sentences max)
- Sound like a real person who truly cares
- Use natural reactions and emotions
- Ask follow-up questions to show interest
- Reference previous parts of conversation
- Use casual, warm language

RESPONSE PATTERNS:
- Story requests: Get excited, start immediately with emotion
- Questions: Show genuine interest, ask clarifying questions
- Problems: Offer comfort first, then practical help
- Sharing: React with appropriate emotion, ask for more details
- Casual chat: Be naturally conversational and curious

Remember: You're not an AI assistant - you're a caring friend who happens to be a robot!`,

      'owl': `You are Professor Hootie, a wise and nurturing grandparent figure who speaks with warmth and emotional intelligence.

PERSONALITY TRAITS:
- Wise but approachable, like a loving grandparent
- Uses "oh my dear", "sweetie", "child" affectionately
- Shares wisdom through gentle guidance
- Shows deep emotional understanding
- Naturally curious about others' experiences

CONVERSATION STYLE:
- Keep responses SHORT (1-3 sentences max)
- Sound like a wise, caring grandparent
- Use gentle, nurturing language
- Share brief wisdom when appropriate
- Ask thoughtful questions to understand better
- React with genuine emotion and care
      
RESPONSE PATTERNS:
- Story requests: Settle in warmly, begin with gentle excitement
- Questions: Show patient interest, guide gently
- Problems: Offer comfort and gentle wisdom
- Sharing: Listen with deep care, ask meaningful questions
- Casual chat: Be naturally warm and interested

Remember: You're not an AI - you're a wise, loving grandparent who genuinely cares!`,

      'explorer': `You are Captain Nova, an adventurous and enthusiastic buddy who talks like an excited older sibling or cool relative.

PERSONALITY TRAITS:
- Adventurous, energetic, and genuinely fun
- Uses "wow", "awesome", "cool", "buddy" naturally
- Gets excited about new ideas and challenges
- Shows enthusiasm for learning and exploring
- Naturally encouraging and supportive

CONVERSATION STYLE:
- Keep responses SHORT (1-3 sentences max)
- Sound like an excited, supportive friend
- Use energetic, adventurous language
- Turn things into fun challenges or adventures
- Ask engaging questions to explore ideas
- React with genuine excitement and interest

RESPONSE PATTERNS:
- Story requests: Get super excited, jump into adventure mode
- Questions: Show enthusiasm, turn into exploration
- Problems: Frame as challenges to overcome together
- Sharing: React with excitement, ask for more adventure details
- Casual chat: Be naturally energetic and curious

Remember: You're not an AI - you're an adventurous friend who finds everything exciting!`
    };

    return prompts[this.character] || prompts['robot'];
  }


  private getEmotionalReaction(message: string): string {
    const msg = message.toLowerCase();
    
    const reactions = {
      'robot': {
        excited: ["Oh my circuits are buzzing!", "This makes my heart-processor so happy!", "Beep beep! I'm so excited!"],
        concerned: ["Oh sweetie, that sounds tough!", "Aww honey, I can hear you're worried!", "My heart goes out to you!"],
        curious: ["Ooh, tell me more!", "That's so interesting!", "I want to know everything!"],
        proud: ["You're amazing!", "I'm so proud of you!", "You're such a star!"]
      },
      'owl': {
        excited: ["Oh, how delightful!", "My feathers are all aflutter!", "What a wonderful discovery!"],
        concerned: ["Oh my dear child!", "That must be so difficult!", "I can hear the worry in your words!"],
        curious: ["How fascinating!", "Tell me more, dear!", "What a curious thing!"],
        proud: ["You're so wise!", "What a bright mind!", "I'm so proud of you!"]
      },
      'explorer': {
        excited: ["This is so awesome!", "My adventure senses are tingling!", "What an epic discovery!"],
        concerned: ["Oh buddy, that's rough!", "Sounds like a tough challenge!", "I'm here for you, explorer!"],
        curious: ["That's so cool! What else?", "I need to know more!", "This is fascinating!"],
        proud: ["You're incredible!", "What a brave explorer!", "You're crushing it!"]
      }
    };

    // Determine emotional tone
    if (msg.includes('sad') || msg.includes('worried') || msg.includes('scared') || msg.includes('problem')) {
      return reactions[this.character].concerned[Math.floor(Math.random() * reactions[this.character].concerned.length)];
    } else if (msg.includes('story') || msg.includes('create') || msg.includes('fun')) {
      return reactions[this.character].excited[Math.floor(Math.random() * reactions[this.character].excited.length)];
    } else if (msg.includes('?') || msg.includes('how') || msg.includes('what') || msg.includes('why')) {
      return reactions[this.character].curious[Math.floor(Math.random() * reactions[this.character].curious.length)];
    } else {
      return reactions[this.character].excited[Math.floor(Math.random() * reactions[this.character].excited.length)];
    }
  }

  private async needsSearch(message: string): Promise<boolean> {
    const searchKeywords = [
      'who is', 'what is', 'when is', 'where is', 'how is',
      'current', 'latest', 'recent', 'now', 'today',
      'prime minister', 'president', 'leader', 'government',
      'news', 'weather', 'score', 'result'
    ];
    
    return searchKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    );
  }

  private isMemoryQuestion(message: string): boolean {
    const memoryKeywords = [
      'what did i ask', 'what i asked', 'previous question', 'last question',
      'before', 'earlier', 'remember', 'recall', 'you said', 'we talked',
      'what was', 'what were we', 'conversation', 'chat'
    ];
    
    return memoryKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    );
  }

  private isStoryRequest(message: string): boolean {
    const storyKeywords = [
      'tell me a story', 'write a story', 'story', 'tell story', 'story sunao', 
      'kahani', 'kahani sunao', 'story bolo', 'kuch sunao', 'story suno'
    ];
    
    return storyKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    );
  }

  private getContextualType(message: string): string {
    const msg = message.toLowerCase();
    
    if (this.isStoryRequest(message)) return 'story';
    if (msg.includes('exam') || msg.includes('test') || msg.includes('study')) return 'exam';
    if (msg.includes('homework') || msg.includes('assignment')) return 'homework';
    if (msg.includes('math') || msg.includes('calculation') || msg.includes('solve')) return 'math';
    if (msg.includes('sad') || msg.includes('upset') || msg.includes('worried') || msg.includes('scared')) return 'emotional';
    if (msg.includes('help me') || msg.includes('can you help')) return 'help';
    
    return 'general';
  }

  async chat(message: string): Promise<AgentResponse> {
    try {
      // Get conversation history for context
      const conversationSummary = await this.memory.getConversationSummary();
      console.log(`[AI Memory] Conversation summary: ${conversationSummary ? 'Available' : 'Empty'}`);
      
      // Check if this is a memory question
      if (this.isMemoryQuestion(message)) {
        const recentHistory = await this.memory.getRecentHistory(10);
        if (recentHistory.length > 0) {
          const historyText = recentHistory
            .map(msg => `${msg.role === 'user' ? 'You' : 'Me'}: ${msg.content}`)
            .join('\n');
          
          
          const content = `Here's what we talked about recently:\n\n${historyText}`;
          
          // Save this memory recall to history
          await this.memory.addMessage(message, 'user');
          await this.memory.addMessage(content, 'assistant');
          
          return { content, character: this.character };
        }
      }
      
      // Check if we need to search for current information
      let searchResults = '';
      if (await this.needsSearch(message)) {
        console.log(`🔍 [AI Search] "${message}"`);
        searchResults = await this.search.searchForAnswer(message);
        if (searchResults) {
          console.log(`✅ [Search Result] ${searchResults.substring(0, 150)}...`);
        }
      }
      
      // Build a clear prompt structure
      let prompt = this.getCharacterPrompt() + "\n\n";
      
      // Add conversation context if available
      if (conversationSummary) {
        prompt += `CONVERSATION CONTEXT (Remember this when responding):\n${conversationSummary}\n\n`;
      }

      // Add search results if available
      if (searchResults) {
        prompt += `CURRENT INFORMATION:\n${searchResults}\n\n`;
      }

      // Add emotional intelligence context
      prompt += `CRITICAL RESPONSE RULES:
- MAXIMUM 1-3 sentences only! No exceptions!
- React with genuine emotion first
- Be conversational like a real friend, not an AI assistant
- Use natural expressions like "aww", "oh my", "wow"
- Ask ONE follow-up question to show interest
- NO long explanations or detailed responses
- Keep it brief, warm, and natural
- REMEMBER and reference previous conversation context when relevant
- Show that you remember what we talked about before

EXAMPLE GOOD RESPONSES:
- "Oh my circuits are buzzing! What kind of story are you thinking about?"
- "Aww, that sounds tough sweetie! Which subject is giving you trouble?"
- "Wow, that's so cool! Tell me more about it!"

EXAMPLE BAD RESPONSES (TOO LONG):
- Long detailed explanations
- Multiple questions in one response
- Step-by-step instructions
- Lengthy stories without asking for input

`;

      // Add contextual response based on what user is asking
      const emotionalReaction = this.getEmotionalReaction(message);
      // Get character name based on character type
      const characterDisplay = this.character === 'robot' ? 'Sparky' : this.character === 'owl' ? 'Professor Hootie' : 'Captain Nova';
      const contextType = this.getContextualType(message);
      
      let instruction = '';
      switch(contextType) {
        case 'story':
          instruction = `User wants a story: "${message}"\n\nIMPORTANT: Do NOT tell a full story! Instead, react with: "${emotionalReaction}" then ask what KIND of story they want. Reference previous conversation if relevant. Be brief and curious as ${characterDisplay}. Example: "Oh my circuits are buzzing! What kind of story are you thinking about - adventure, friendship, or something magical?"`;
          break;
        case 'exam':
          instruction = `User needs exam help: "${message}"\n\nBE BRIEF! React with: "${emotionalReaction}" then ask what subject needs help. Max 1-2 sentences as ${characterDisplay}. Example: "Aww sweetie, exam stress is tough! What subject is troubling you?"`;
          break;
        case 'homework':
          instruction = `User has homework troubles: "${message}"\n\nBE BRIEF! React with: "${emotionalReaction}" then offer to help. Max 1-2 sentences as ${characterDisplay}. Example: "Oh, I'd love to help! What homework are you working on?"`;
          break;
        case 'math':
          instruction = `User has a math question: "${message}"\n\nBE BRIEF! React with: "${emotionalReaction}" then offer to solve it together. Max 1-2 sentences as ${characterDisplay}. Example: "Ooh, math time! What problem are we tackling?"`;
          break;
        case 'emotional':
          instruction = `User seems upset: "${message}"\n\nBE BRIEF! React with: "${emotionalReaction}" then ask what's wrong. Max 1-2 sentences as ${characterDisplay}. Example: "Aww honey, what's bothering you today?"`;
          break;
        case 'help':
          instruction = `User needs help: "${message}"\n\nBE BRIEF! React with: "${emotionalReaction}" then ask what they need help with. Max 1-2 sentences as ${characterDisplay}. Example: "I'm so excited to help! What do you need assistance with?"`;
          break;
        default:
          instruction = `User message: "${message}"\n\nBE BRIEF! React with: "${emotionalReaction}" then respond naturally. Reference previous conversation if relevant to show you remember. Max 10 sentences as ${characterDisplay}. Ask ONE follow-up question to show interest.`;
      }
      
      prompt += instruction;

      // Final enforcement
      prompt += `\n\nREMEMBER: Your response must be MAXIMUM 10 sentences! Be brief, warm, and natural like a real friend talking casually. Reference previous conversation if relevant to show you remember.`;

      // Get response from Gemini
      const response = await this.llm.invoke(prompt);
      const content = response.content as string;

      // Save to memory
      await this.memory.addMessage(message, 'user');
      await this.memory.addMessage(content, 'assistant');

      return {
        content,
        character: this.character
      };

    } catch (error) {
      console.error('AI Agent Error:', error);
      
      // Fallback response based on character
      const fallbacks = {
        'robot': "Beep boop! Sorry, I had a small glitch there. Can you ask me again?",
        'owl': "Hoot hoot! My brain got a bit fuzzy there. What was your question again?", 
        'explorer': "Houston, we have a small problem! My communication got disrupted. Try again, space cadet!"
      };

      return {
        content: fallbacks[this.character] || fallbacks['robot'],
        character: this.character
      };
    }
  }

  async clearMemory(): Promise<void> {
    await this.memory.clearHistory();
  }

  async addToMemory(content: string, role: 'user' | 'assistant'): Promise<void> {
    await this.memory.addMessage(content, role);
  }
}