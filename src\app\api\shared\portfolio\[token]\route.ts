import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/shared/portfolio/[token] - Get shared portfolio data
export async function GET(
  request: NextRequest,
  { params }: { params: { token: string } }
) {
  try {
    const token = params.token;

    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Share token is required' 
        },
        { status: 400 }
      );
    }

    // Decode the share token
    let tokenData;
    try {
      const decodedToken = Buffer.from(token, 'base64url').toString('utf-8');
      tokenData = JSON.parse(decodedToken);
    } catch (error) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid share token' 
        },
        { status: 400 }
      );
    }

    const { userId, timestamp, type } = tokenData;

    if (!userId || !timestamp || type !== 'portfolio') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid share token format' 
        },
        { status: 400 }
      );
    }

    // Check if token is expired (30 days)
    const tokenAge = Date.now() - timestamp;
    const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
    
    if (tokenAge > thirtyDaysInMs) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Share link has expired' 
        },
        { status: 410 }
      );
    }

    // Get user profile
    const profile = await prisma.profile.findUnique({
      where: { id: userId },
      select: {
        full_name: true
      }
    });

    if (!profile) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'User not found' 
        },
        { status: 404 }
      );
    }

    // Get user's content
    const userContent = await prisma.userContent.findMany({
      where: {
        user_id: userId,
        type: {
          in: ['story', 'art', 'music']
        }
      },
      include: {
        challenge: {
          select: {
            title: true,
            type: true,
            difficulty: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      },
      take: 20 // Limit to recent 20 projects for sharing
    });

    // Calculate statistics
    const stats = userContent.reduce((acc, content) => {
      acc.total++;
      switch (content.type) {
        case 'story':
          acc.stories++;
          break;
        case 'art':
          acc.artwork++;
          break;
        case 'music':
          acc.music++;
          break;
      }
      return acc;
    }, { stories: 0, artwork: 0, music: 0, total: 0 });

    // Get the share record to find the message
    const shareRecord = await prisma.userContent.findFirst({
      where: {
        user_id: userId,
        type: 'share',
        content_metadata: {
          path: ['shareToken'],
          equals: token
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    const portfolioData = {
      childName: profile.full_name || 'Little Spark User',
      projects: userContent.map(project => ({
        id: project.id,
        type: project.type,
        title: project.title,
        content_metadata: project.content_metadata,
        preview_url: project.preview_url,
        created_at: project.created_at,
        challenge: project.challenge
      })),
      stats,
      message: shareRecord?.content_metadata?.message || null,
      sharedAt: shareRecord?.created_at || new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      portfolio: portfolioData
    });

  } catch (error) {
    console.error('Error fetching shared portfolio:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to load portfolio' 
      },
      { status: 500 }
    );
  }
}
