"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";

import AnnouncementBanner from "@/components/shared/AnnouncementBanner";
import SubscriptionHeader from "@/components/subscription/SubscriptionHeader";
import { SubscriptionPlans } from "@/components/subscription/SubscriptionPlans";
import GiftCodeRedemption from "@/components/subscription/GiftCodeRedemption";
import GiftSubscriptionCard from "@/components/subscription/GiftSubscriptionCard";
import WhyChooseLittleSpark from "@/components/subscription/WhyChooseLittleSpark";
import { getSubscriptionPlans } from "@/components/subscription/planData";
import { SubscriptionPlan } from "@/components/subscription/SubscriptionPlans";
import { Accordion } from "@/components/ui/Accordion";
import { ChevronDown } from "lucide-react";
import { fredoka } from "@/lib/fonts";
import StripePaymentForm from "@/components/checkout/StripePaymentForm";
import { useAuth } from '@/hooks/useAuth';
import { useProfile } from '@/hooks/useProfile';
import { toast } from 'sonner';

// Simple FAQ Item component matching home page style
interface FAQItemProps {
    question: string;
    answer: string;
    index: number;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, index }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div
            className="border-b border-gray-200 rounded-lg overflow-hidden animate-fadeInUp"
            style={{ animationDelay: `${0.1 + index * 0.05}s` }}
        >
            <button
                className="w-full px-4 sm:px-6 py-3 sm:py-4 text-left bg-white transition-colors duration-200 
                   flex items-center justify-between group"
                onClick={() => setIsOpen(!isOpen)}
            >
                <h3
                    className={`text-sm sm:text-md font-bold text-gray-800 hover:underline ${fredoka.className} pr-4`}
                >
                    {question}
                </h3>
                <ChevronDown
                    className={`h-5 w-5 text-gray-500 transition-transform duration-200 flex-shrink-0 
                     ${
                         isOpen ? "rotate-180" : ""
                     } group-hover:text-littlespark-primary`}
                />
            </button>

            <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                    isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                }`}
            >
                <div className="px-4 sm:px-6 pb-4 sm:pb-5 pt-1 sm:pt-2">
                    <p className="text-gray-700 text-xs sm:text-sm font-light leading-relaxed">
                        {answer}
                    </p>
                </div>
            </div>
        </div>
    );
};

// Simple FAQ Section component matching home page style
const PricingFAQ = () => {
    const faqs = [
        {
            question: "How do I cancel my subscription?",
            answer: "You can cancel your subscription at any time from your account settings. Once canceled, your subscription will remain active until the end of your current billing period.",
        },
        {
            question: "What happens when my subscription ends?",
            answer: "When your subscription ends, you'll lose access to all premium features and any content created with your Little Spark account. We recommend downloading any projects you want to keep before your subscription expires.",
        },
        {
            question: "Can I switch between subscription plans?",
            answer: "Yes, you can change your subscription plan at any time from your account settings. If you upgrade, the change will take effect immediately and you'll be charged the prorated difference.",
        },
        {
            question: "How do gift subscriptions work?",
            answer: "Gift subscriptions allow you to purchase a Little Spark subscription for someone else. The recipient will receive an email with instructions on how to activate their gift subscription. You can schedule the delivery for any date you choose.",
        },
    ];

    return (
        <section className="py-12 sm:py-16 lg:py-20 bg-white">
            <div className="container mx-auto px-4">
                <div className="text-center mb-10 sm:mb-12 max-w-3xl mx-auto">
                    <h2
                        className={`text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-5 text-gray-800 ${fredoka.className}`}
                    >
                        Frequently Asked Questions
                    </h2>
                    <p className="text-gray-600 text-base sm:text-lg">
                        Get answers to common questions about our pricing and
                        subscriptions
                    </p>
                </div>

                <div className="max-w-3xl mx-auto">
                    <Accordion type="single" collapsible className="w-full">
                        {faqs.map((faq, index) => (
                            <FAQItem
                                key={index}
                                question={faq.question}
                                answer={faq.answer}
                                index={index}
                            />
                        ))}
                    </Accordion>
                </div>
            </div>
        </section>
    );
};

const PricingPage = () => {
    const plans = getSubscriptionPlans();
    const router = useRouter();
    const { user } = useAuth();
    const { profile } = useProfile();
    const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);

    const handleSelectPlan = (plan: SubscriptionPlan) => {
        console.log("Selected plan:", plan);
        setSelectedPlan(plan);
    };

    const handleGiftPlan = (plan: SubscriptionPlan) => {
        // Handle gift plan selection
        console.log("Gift plan selected:", plan);
        // You can implement gift purchase flow here
    };

    return (
        <div className="min-h-screen bg-white">
            <div className="container mx-auto py-8 sm:py-12 px-4">
                <AnnouncementBanner />
                <SubscriptionHeader />
                <div className="mb-12 sm:mb-16">
                    <SubscriptionPlans
                        plans={plans}
                        onSelectPlan={selectedPlan ? undefined : handleSelectPlan}
                    />
                </div>
                <div className="max-w-lg mx-auto mb-12 sm:mb-16">
                    <GiftCodeRedemption />
                </div>
                <div className="max-w-2xl mx-auto mb-12 sm:mb-16">
                    <GiftSubscriptionCard
                        plans={plans}
                        onGiftPlan={handleGiftPlan}
                    />
                </div>
                <WhyChooseLittleSpark />
                {selectedPlan && (
                    <div className="max-w-2xl mx-auto mb-12 sm:mb-16">
                        <div className="bg-white rounded-lg shadow-sm border p-4 sm:p-6">
                            <h2 className="text-xl sm:text-2xl font-bold text-center mb-4">
                                Complete Your Payment
                            </h2>
                            <StripePaymentForm
                                planId={selectedPlan.planId}
                                onSuccess={() => router.push('/thank-you')}
                                onError={(error) => toast.error(`Payment failed: ${error}`)}
                                userEmail={user?.email || ''}
                                userName={profile?.full_name || ''}
                            />
                        </div>
                    </div>
                )}
            </div>

            <PricingFAQ />
        </div>
    );
};

export default PricingPage;
