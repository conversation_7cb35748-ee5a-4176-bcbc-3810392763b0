import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Sample user content for testing the projects section
const sampleUserContent = [
  {
    type: 'story',
    title: 'The Magical Forest Adventure',
    content_metadata: {
      fullContent: `Once upon a time, in a magical forest filled with talking animals and glowing flowers, there lived a brave little rabbit named <PERSON>. <PERSON> had silver fur that sparkled in the moonlight and eyes as blue as the clearest sky.

One day, <PERSON> discovered that the forest's magic was fading. The flowers were losing their glow, and the animals were forgetting how to speak. Determined to save her home, <PERSON> embarked on a quest to find the legendary Crystal of Harmony.

Through dark caves and across rushing rivers, <PERSON> traveled with her new friends: <PERSON><PERSON> the wise owl and <PERSON> the cheerful fox. Together, they solved riddles, helped other creatures, and learned that the real magic was in their friendship and kindness.

When they finally found the Crystal of Harmony hidden in an ancient tree, <PERSON> realized that the crystal's power came from the love and care of all the forest creatures. By working together and believing in each other, they restored the forest's magic and lived happily ever after.`,
      preview: 'A brave little rabbit named <PERSON> embarks on a magical quest to save her forest home...',
      storyTheme: 'friendship',
      storyGenre: 'fantasy',
      storyStage: 'complete'
    },
    preview_url: null,
    challenge_id: null
  },
  {
    type: 'art',
    title: 'Rainbow Dragon',
    content_metadata: {
      prompt: 'A friendly dragon with rainbow scales flying through fluffy white clouds',
      artStyle: 'cartoon',
      aspectRatio: '1:1',
      description: 'A colorful dragon artwork created with AI'
    },
    preview_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop',
    challenge_id: null
  },
  {
    type: 'story',
    title: 'The Robot Helper',
    content_metadata: {
      fullContent: `In a busy city of the future, there lived a small robot named Beep. Beep was different from other robots because he had a big heart and loved to help people.

Every morning, Beep would roll through the streets looking for ways to help. He helped old ladies cross the street, carried heavy bags for shoppers, and even helped lost pets find their way home.

One day, Beep noticed that the city's main computer was acting strange. Traffic lights were blinking randomly, and elevators were going to the wrong floors. Beep knew he had to help!

Using his special diagnostic tools, Beep discovered that the computer just needed a friend. It had been working alone for so long that it had forgotten how to be happy. Beep taught the computer about friendship, and together they made the city a better place for everyone.`,
      preview: 'A helpful robot named Beep saves the day in a futuristic city...',
      storyTheme: 'technology',
      storyGenre: 'science fiction',
      storyStage: 'complete'
    },
    preview_url: null,
    challenge_id: 'weekly-robot'
  },
  {
    type: 'music',
    title: 'Happy Sunshine Song',
    content_metadata: {
      description: 'A cheerful melody that sounds like sunshine and rainbows',
      mood: 'happy',
      style: 'upbeat',
      duration: 30
    },
    preview_url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    challenge_id: null
  },
  {
    type: 'art',
    title: 'Underwater Castle',
    content_metadata: {
      prompt: 'A beautiful underwater castle with colorful fish swimming around it',
      artStyle: 'fantasy',
      aspectRatio: '16:9',
      description: 'An underwater fantasy scene'
    },
    preview_url: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop',
    challenge_id: null
  },
  {
    type: 'story',
    title: 'The Space Explorer',
    content_metadata: {
      fullContent: `Captain Zara was the youngest space explorer in the galaxy. With her trusty spaceship, the Starlight, she traveled from planet to planet, discovering new worlds and making friends with alien creatures.

On the planet Zephyr, she met the Glowing Jellies - creatures that looked like floating jellyfish but could change colors to communicate. They were sad because their home was getting too dark.

Zara learned that Zephyr's sun was dimming. Using her knowledge of science and her kind heart, she helped the Glowing Jellies build special mirrors that could collect and reflect light from distant stars.

Soon, Zephyr was bright and beautiful again. The Glowing Jellies thanked Zara by teaching her their color language, and she promised to visit them again on her next space adventure.`,
      preview: 'Captain Zara explores the galaxy and helps alien friends on planet Zephyr...',
      storyTheme: 'space',
      storyGenre: 'science fiction',
      storyStage: 'complete'
    },
    preview_url: null,
    challenge_id: null
  },
  {
    type: 'music',
    title: 'Adventure Theme',
    content_metadata: {
      description: 'An exciting musical theme for epic adventures',
      mood: 'adventurous',
      style: 'orchestral',
      duration: 45
    },
    preview_url: null,
    challenge_id: null
  },
  {
    type: 'art',
    title: 'Magical Unicorn',
    content_metadata: {
      prompt: 'A beautiful unicorn with a flowing mane standing in a field of flowers',
      artStyle: 'fantasy',
      aspectRatio: '1:1',
      description: 'A magical unicorn in a flower field'
    },
    preview_url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop',
    challenge_id: null
  }
];

async function seedUserContent(userId: string) {
  console.log('🌱 Seeding user content for user:', userId);

  try {
    // Check if user exists
    const user = await prisma.profile.findUnique({
      where: { id: userId }
    });

    if (!user) {
      console.error('❌ User not found:', userId);
      return;
    }

    // Clear existing content for this user (optional)
    const existingCount = await prisma.userContent.count({
      where: { user_id: userId }
    });

    if (existingCount > 0) {
      console.log(`📝 User already has ${existingCount} content items. Skipping seed.`);
      return;
    }

    // Create sample content
    const createdContent = [];
    
    for (let i = 0; i < sampleUserContent.length; i++) {
      const content = sampleUserContent[i];
      
      // Create content with staggered timestamps
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - (sampleUserContent.length - i));
      createdAt.setHours(createdAt.getHours() - Math.floor(Math.random() * 12));

      const userContent = await prisma.userContent.create({
        data: {
          user_id: userId,
          type: content.type,
          title: content.title,
          content_metadata: content.content_metadata,
          preview_url: content.preview_url,
          challenge_id: content.challenge_id,
          created_at: createdAt,
          updated_at: createdAt
        }
      });

      createdContent.push(userContent);
      console.log(`✅ Created ${content.type}: ${content.title}`);
    }

    console.log(`🎉 Successfully seeded ${createdContent.length} content items!`);
    
    // Display summary
    const summary = createdContent.reduce((acc, content) => {
      acc[content.type] = (acc[content.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    console.log('📊 Content Summary:');
    Object.entries(summary).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} items`);
    });

  } catch (error) {
    console.error('❌ Error seeding user content:', error);
  }
}

// Main execution
async function main() {
  const userId = process.argv[2];
  
  if (!userId) {
    console.log('Usage: npx ts-node scripts/seedUserContent.ts <user-id>');
    console.log('Example: npx ts-node scripts/seedUserContent.ts 123e4567-e89b-12d3-a456-426614174000');
    return;
  }

  await seedUserContent(userId);
}

if (require.main === module) {
  main()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

export { seedUserContent };
