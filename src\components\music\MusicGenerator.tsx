
import React from 'react';

import MusicTitleInput from './components/generator/MusicTitleInput';
import MusicDescriptionInput from './components/generator/MusicDescriptionInput';
import MusicMoodSelector from './components/generator/MusicMoodSelector';
import MusicStyleSelector from './components/generator/MusicStyleSelector';
import GenerateMusicButton from './components/generator/GenerateMusicButton';
import { useMusicGenerator } from './components/generator/useMusicGenerator';
import { Button } from '@/components/ui/button';
import { Save, Download } from 'lucide-react';

interface MusicGeneratorProps {
  onGenerate: () => void;
  onAudioGenerated?: (audioUrl: string, title?: string) => void;
  onGenerationError?: (error: string) => void;
}

const MusicGenerator = ({ 
  onGenerate, 
  onAudioGenerated, 
  onGenerationError 
}: MusicGeneratorProps) => {
  const {
    musicTitle,
    setMusicTitle,
    musicDescription,
    setMusicDescription,
    songTheme,
    setSongTheme,
    // musicDuration,
    // setMusicDuration,
    musicLyrics,
    selectedStyle,
    setSelectedStyle,
    selectedMood,
    setSelectedMood,
    isGeneratingDescription,
    isImprovingDescription,
    // isGeneratingLyrics,
    isGeneratingMusic,
    generatedAudioUrl,
    isSaving,
    handleGenerateDescription,
    handleImproveDescription,
    // handleGenerateLyrics,
    handleGenerate,
    handleSaveToPortfolio,
    handleDownload
  } = useMusicGenerator({ 
    onGenerate,
    onGenerationError
  });

  // Pass the generated audio URL and title to the parent component when it changes
  React.useEffect(() => {
    if (generatedAudioUrl && onAudioGenerated) {
      onAudioGenerated(generatedAudioUrl, musicTitle);
    }
  }, [generatedAudioUrl, musicTitle, onAudioGenerated]);

  return (
    <div className="lg:col-span-1 space-y-6 bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-bold font-quicksand">Music Generator</h2>
      
      <div className="space-y-4">
        <MusicTitleInput 
          musicTitle={musicTitle} 
          setMusicTitle={setMusicTitle} 
        />
        
        <MusicDescriptionInput 
          musicDescription={musicDescription}
          setMusicDescription={setMusicDescription}
          handleGenerateDescription={handleGenerateDescription}
          isGeneratingDescription={isGeneratingDescription}
          handleImproveDescription={handleImproveDescription}
          isImprovingDescription={isImprovingDescription}
        />
        <div>
          <label htmlFor="songTheme" className="block text-sm font-medium mb-1">Song Theme (optional)</label>
          <input
            id="songTheme"
            type="text"
            value={songTheme}
            onChange={(e) => setSongTheme(e.target.value)}
            placeholder="e.g. love, adventure, celebration"
            className="w-full rounded-md border px-3 py-2"
          />
          <p className="text-sm text-gray-500 mt-1">Try themes like &quot;adventure&quot;, &quot;friendship&quot;, &quot;summer sunset&quot;, &quot;celebration&quot;</p>
        </div>
        
        <MusicMoodSelector 
          selectedMood={selectedMood}
          setSelectedMood={setSelectedMood}
        />
        
        <MusicStyleSelector 
          selectedStyle={selectedStyle}
          setSelectedStyle={setSelectedStyle}
        />
        
        {/* Duration selector removed – default duration is 10 seconds */}
        
        {/* Generate Lyrics Button */}
        {/* <Button
          onClick={handleGenerateLyrics}
          disabled={isGeneratingLyrics || !musicTitle}
          variant="outline"
          size="sm"
          className="mt-2"
        >
          {isGeneratingLyrics ? 'Generating Lyrics...' : 'Generate Lyrics'}
        </Button> */}
         
        <GenerateMusicButton 
          handleGenerate={handleGenerate}
          isGeneratingMusic={isGeneratingMusic}
          isDisabled={musicDescription.length < 10}
        />
        
        {musicLyrics && (
          <div>
            <label htmlFor="musicLyrics" className="block text-sm font-medium mb-1">Lyrics</label>
            <textarea
              id="musicLyrics"
              value={musicLyrics}
              readOnly
              rows={10}
              className="w-full rounded-md border px-3 py-2 bg-gray-50"
            />
          </div>
        )}
        
        {generatedAudioUrl && (
          <div className="mt-4 flex flex-wrap gap-2">
            <Button 
              variant="outline" 
              onClick={handleSaveToPortfolio}
              className="flex items-center gap-1"
              disabled={isSaving}
            >
              <Save className="h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save to Portfolio'}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleDownload}
              className="flex items-center gap-1"
            >
              <Download className="h-4 w-4" />
              Download
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MusicGenerator;
