
import React from 'react';
import { Wand2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface GenerateMusicButtonProps {
  handleGenerate: () => void;
  isGeneratingMusic: boolean;
  isDisabled: boolean;
}

const GenerateMusicButton = ({ 
  handleGenerate, 
  isGeneratingMusic, 
  isDisabled 
}: GenerateMusicButtonProps) => {
  return (
    <Button 
      onClick={handleGenerate} 
      className="w-full bg-spark-lavender hover:bg-spark-lavender/90 gap-2 min-h-[44px] text-sm sm:text-base"
      disabled={isGeneratingMusic || isDisabled}
    >
      {isGeneratingMusic ? (
        <>
          <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <span className="sm:hidden">Creating...</span>
          <span className="hidden sm:inline">Generating Music...</span>
        </>
      ) : (
        <>
          <Wand2 className="h-4 w-4" />
          <span className="sm:hidden">Create Music</span>
          <span className="hidden sm:inline">Generate Music</span>
        </>
      )}
    </Button>
  );
};

export default GenerateMusicButton;
