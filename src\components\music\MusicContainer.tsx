"use client";
import React from 'react';
import MusicContent from './components/MusicContent';
import MusicLayout from './components/MusicLayout';

const MusicContainer = () => {
  const [learningMode, setLearningMode] = React.useState(true);

  const toggleLearningMode = () => {
    setLearningMode(!learningMode);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-spark-lavender/5 py-8 px-4">
      <div className="container mx-auto max-w-6xl">
        <MusicLayout
          learningMode={learningMode}
          toggleLearningMode={toggleLearningMode}
        >
          <MusicContent />
        </MusicLayout>
      </div>
    </div>
  );
};

export default MusicContainer;
