import { NextRequest, NextResponse } from 'next/server';
import Replicate from 'replicate';

export async function POST(req: NextRequest) {
  const { prompt } = await req.json();
  console.log('🔥 API: Received prompt:', prompt);
  
  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required' }, { status: 400 });
  }

  const replicate = new Replicate({ auth: process.env.REPLICATE_API_TOKEN });
  
  // Generate line art/coloring page outline
  const input = {
    prompt: `vector-style coloring book line art, crisp black outlines of ${prompt}, consistent uniform line weight, enclosed shapes, minimal interior details, white background, high contrast, simple clear edges, easy for coloring`,
    negative_prompt: "color, shading, gradient, fill, photorealism, blur, color artifacts, thin lines, noise, messy",  
    width: 512,
    height: 512,
    num_outputs: 1,
    guidance_scale: 12,
    num_inference_steps: 30,
    scheduler: "DPMSolverMultistep"
  };

  console.log('🔥 API: Input to Replicate:', input);

  try {
    const output = await replicate.run(
      'stability-ai/stable-diffusion:ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4',
      { input }
    ) as string[];

    console.log('🔥 API: Replicate output:', output);

    if (output && Array.isArray(output) && output.length > 0) {
      const imageResult = output[0];
      console.log('🔥 API: Image result type:', typeof imageResult);
      
      // Check if it's a ReadableStream
      if (imageResult && typeof imageResult === 'object' && 'locked' in imageResult) {
        console.log('🔥 API: Converting ReadableStream to blob');
        const response = new Response(imageResult as ReadableStream);
        const blob = await response.blob();
        const buffer = await blob.arrayBuffer();
        const base64 = Buffer.from(buffer).toString('base64');
        const dataUrl = `data:image/png;base64,${base64}`;
        
        console.log('🔥 API: Success - returning data URL');
        return NextResponse.json({ image: dataUrl });
      } else {
        // If it's already a URL string
        console.log('🔥 API: Success - returning image URL:', imageResult);
        return NextResponse.json({ image: imageResult });
      }
    } else {
      console.log('🔥 API: Error - no image in output');
      return NextResponse.json({ error: 'No image generated' }, { status: 500 });
    }
  } catch (err) {
    console.error('🔥 API: Replicate API error:', err);
    return NextResponse.json({
      error: 'Failed to generate image',
      details: err instanceof Error ? err.message : 'Unknown error'
    }, { status: 500 });
  }
} 