import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { BookO<PERSON>, Palette, Music, Filter } from "lucide-react";

interface ContentStats {
    stories: number;
    artwork: number;
    music: number;
    total: number;
}

interface ProjectsFiltersProps {
    selectedFilter: string;
    onFilterChange: (filter: string) => void;
    contentStats: ContentStats;
}

const ProjectsFilters: React.FC<ProjectsFiltersProps> = ({
    selectedFilter,
    onFilterChange,
    contentStats
}) => {
    const filters = [
        {
            id: "all",
            label: "All Projects",
            icon: Filter,
            count: contentStats.total,
            color: "bg-gray-100 text-gray-800"
        },
        {
            id: "story",
            label: "Stories",
            icon: BookOpen,
            count: contentStats.stories,
            color: "bg-blue-100 text-blue-800"
        },
        {
            id: "art",
            label: "Artwork",
            icon: Palette,
            count: contentStats.artwork,
            color: "bg-green-100 text-green-800"
        },
        {
            id: "music",
            label: "Music",
            icon: Music,
            count: contentStats.music,
            color: "bg-purple-100 text-purple-800"
        }
    ];

    return (
        <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
                <div className="flex gap-6">
                    {filters.map((filter) => {
                        const Icon = filter.icon;
                        const isSelected = selectedFilter === filter.id;

                        return (
                            <button
                                key={filter.id}
                                type="button"
                                onClick={() => {
                                    console.log('Filter clicked:', filter.id);
                                    onFilterChange(filter.id);
                                }}
                                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                                    isSelected
                                        ? "bg-blue-50 text-blue-600 border-b-2 border-blue-600"
                                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
                                }`}
                            >
                                <Icon className="h-4 w-4" />
                                {filter.label}
                            </button>
                        );
                    })}
                </div>

                <Button
                    variant="outline"
                    className="text-teal-600 border-teal-600 hover:bg-teal-50"
                >
                    <Filter className="h-4 w-4 mr-2" />
                    Sort & Filter
                </Button>
            </div>

            {contentStats.total === 0 && (
                <div className="text-center py-12">
                    <p className="text-gray-500 text-lg mb-2">You haven't created any projects yet</p>
                    <p className="text-gray-400 text-sm">
                        Start creating stories, artwork, or coded creatures to see them here
                    </p>
                </div>
            )}
        </div>
    );
};

export default ProjectsFilters;
