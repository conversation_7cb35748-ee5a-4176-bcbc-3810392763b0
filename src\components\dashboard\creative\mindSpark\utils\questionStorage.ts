import { Question } from '../types';
import { supabase } from '@/lib/supabase/client';
import { convertToJson } from './types';

// Save question history to user's content
export const saveQuestionHistory = async (userId: string, questions: Question[]): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('user_content')
      .select('id')
      .eq('user_id', userId)
      .eq('type', 'mindspark_history')
      .single();
      
    if (error && error.code !== 'PGRST116') {
      console.error('Error checking for existing history:', error);
      return false;
    }
    
    // Convert questions to JSON-safe format
    const safeQuestions = convertToJson(questions);
    
    // Update or insert logic
    if (data) {
      // Update existing record
      const { error: updateError } = await supabase
        .from('user_content')
        .update({ content_metadata: { questions: safeQuestions } })
        .eq('id', data.id);
      
      if (updateError) {
        console.error('Error updating history:', updateError);
        return false;
      }
    } else {
      // Insert new record
      const { error: insertError } = await supabase
        .from('user_content')
        .insert({
          user_id: userId,
          type: 'mindspark_history',
          title: 'Mind Spark History',
          content_metadata: { questions: safeQuestions }
        });
      
      if (insertError) {
        console.error('Error inserting history:', insertError);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error saving question history:', error);
    return false;
  }
};

// Fetch question history for a user
export const fetchQuestionHistory = async (): Promise<Question[]> => {
  try {
    // Placeholder function - return demo data instead of fetching from Supabase
    console.log('fetchQuestionHistory: Using placeholder data (not fetching from database)');
    
    // Demo question history data
    const demoQuestions: Question[] = [
      {
        id: 'demo-q1',
        question: 'What would you create if you could invent anything?',
        answers: ['A flying bicycle', 'A time machine', 'A robot helper', 'Magic paintbrush'],
        type: 'multiple-choice',
        difficulty: 'easy',
        topic: 'creativity',
        userAnswer: 'A flying bicycle',
        userAnswerIndex: 0,
      },
      {
        id: 'demo-q2',
        question: 'If you could have any superpower, what would it be?',
        answers: ['Flying', 'Super strength', 'Invisibility', 'Time travel'],
        type: 'multiple-choice',
        difficulty: 'easy',
        topic: 'imagination',
        userAnswer: 'Flying',
        userAnswerIndex: 0,
      }
    ];
    
    return demoQuestions;
  } catch (error) {
    console.error('Error in fetchQuestionHistory:', error);
    return [];
  }
};
