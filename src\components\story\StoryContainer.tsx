"use client";
import React from "react";
import { StoryProvider, useStory } from "./StoryContext";
import StoryHeader from "./StoryHeader";
import StoryTitle from "./StoryTitle";
import ReaderAgeSelector from "./ReaderAgeSelector";
import StoryFormatSelector from "./StoryFormatSelector";
import StoryContent from "./StoryContent";
// import CreativeAssistants from "./CreativeAssistants";
import StoryActionButtons from "./StoryActionButtons";
import StoryHelpDialog from "./StoryHelpDialog";
import EducationalBenefits from "./EducationalBenefits";
import StoryLearningGoals from "./StoryLearningGoals";

const StoryPageContent = () => {
    const { helpDialogOpen, setHelpDialogOpen } = useStory();

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-6xl mx-auto">
                <StoryHeader />
                <div className="mb-6" />
                <StoryTitle />
                <div className="mb-3" />
                <ReaderAgeSelector />
                <div className="mb-2" />
                <StoryFormatSelector />
                <div className="mb-6">
                    <h3 className="text-lg font-medium text-center mb-3">
                        Your Story
                    </h3>
                    <StoryContent />
                </div>
                <StoryActionButtons />
                <EducationalBenefits />
                <StoryLearningGoals />
                {/* <CreativeAssistants /> */}
                <StoryHelpDialog
                    open={helpDialogOpen}
                    onOpenChange={setHelpDialogOpen}
                />
            </div>
        </div>
    );
};

const StoryContainer = () => {
    return (
        <StoryProvider>
            <StoryPageContent />
        </StoryProvider>
    );
};

export default StoryContainer;
