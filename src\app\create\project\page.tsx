"use client";
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, FolderOpen } from "lucide-react";
import Link from "next/link";
import { fredoka } from "@/lib/fonts";

export default function ProjectPage() {
    return (
        <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-6xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4">
                            <Link href="/dashboard">
                                <Button
                                    variant="ghost"
                                    className={`${fredoka.className} text-gray-600 hover:text-gray-900 font-bold text-lg sm:text-xl px-0 sm:px-4`}
                                >
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    <span className="hidden sm:inline">Back to</span>{" "}
                                    Dashboard
                                </Button>
                            </Link>
                        </div>

                        <div className="flex items-center gap-2 mb-4">
                            <FolderOpen className="h-5 w-5 sm:h-6 sm:w-6 text-[#9747FF]" />
                            <h1
                                className={`${fredoka.className} text-xl sm:text-2xl md:text-3xl font-bold`}
                            >
                                Interactive Project Builder
                            </h1>
                        </div>
                    </div>

                    {/* Simple Content */}
                    <div className="bg-white rounded-xl shadow-sm p-8 text-center">
                        <h2 className="text-2xl font-bold mb-4">Project Builder</h2>
                        <p className="text-gray-600 mb-6">
                            Create and organize your creative projects with AI assistance.
                        </p>
                        <div className="space-y-4">
                            <div className="p-4 bg-blue-50 rounded-lg">
                                <h3 className="font-semibold text-blue-800">✨ Coming Soon</h3>
                                <p className="text-blue-600 text-sm">
                                    Full project builder functionality is being developed.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
