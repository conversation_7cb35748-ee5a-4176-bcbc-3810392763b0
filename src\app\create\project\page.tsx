"use client";
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, FolderOpen } from "lucide-react";
import Link from "next/link";
import { fredoka } from "@/lib/fonts";

export default function ProjectPage() {
    return (
        <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-6xl mx-auto">
                    {/* Header */}
                    <div className="mb-8">
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0 mb-4">
                            <Link href="/dashboard">
                                <Button
                                    variant="ghost"
                                    className={`${fredoka.className} text-gray-600 hover:text-gray-900 font-bold text-lg sm:text-xl px-0 sm:px-4`}
                                >
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    <span className="hidden sm:inline">Back to</span>{" "}
                                    Dashboard
                                </Button>
                            </Link>
                        </div>

                        <div className="flex items-center gap-2 mb-4">
                            <FolderOpen className="h-5 w-5 sm:h-6 sm:w-6 text-[#9747FF]" />
                            <h1
                                className={`${fredoka.className} text-xl sm:text-2xl md:text-3xl font-bold`}
                            >
                                Interactive Project Builder
                            </h1>
                        </div>
                    </div>

                    {/* Step 1: Basic Form */}
                    <div className="bg-white rounded-xl shadow-sm p-8">
                        <h2 className="text-2xl font-bold text-center mb-6">Create Your Project</h2>

                        {/* Project Title */}
                        <div className="mb-6 text-center flex flex-col items-center">
                            <h3 className="text-lg font-medium mb-2">Project Title</h3>
                            <input
                                type="text"
                                placeholder="Enter a creative title for your project"
                                className="mb-4 rounded-full px-4 py-4 placeholder:text-center placeholder:text-gray-400 border-2 border-gray-200 focus:border-blue-500 focus:outline-none w-full max-w-md"
                            />
                        </div>

                        {/* Project Description */}
                        <div className="mb-6 text-center flex flex-col items-center">
                            <h3 className="text-lg font-medium mb-2">Project Description</h3>
                            <textarea
                                placeholder="Describe what your project is about..."
                                className="mb-4 rounded-xl px-4 py-4 placeholder:text-center placeholder:text-gray-400 min-h-[100px] resize-none border-2 border-gray-200 focus:border-blue-500 focus:outline-none w-full max-w-md"
                            />
                        </div>

                        {/* Simple Save Button */}
                        <div className="text-center">
                            <button className="bg-blue-500 text-white px-8 py-3 rounded-full hover:bg-blue-600 transition-colors">
                                Save Project
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
