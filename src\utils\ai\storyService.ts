import { toast } from 'sonner';
import { isBlocked, incrementStrike, getRemainingBlockMinutes } from './strikeTracker';
// TODO: Replace with custom backend API calls
// import { callAIService } from '../api/apiClient';

// TODO: Replace this with your custom story AI API endpoint
const callCustomStoryAI = async (params: Record<string, unknown>): Promise<string> => {
  // Placeholder for future custom backend integration
  try {
    console.log('[PLACEHOLDER] Would call story AI service with:', params);
    return "Once upon a time, in a magical land far away, there lived a brave young adventurer who discovered something amazing...";
  } catch (error) {
    console.error('Error calling custom story AI service:', error);
    return "Let's create an amazing story together! What should we write about?";
  }
};

// Story generation service using the API endpoint
const callStoryAPI = async (params: {
  prompt: string;
  type: 'story-idea' | 'story-continuation' | 'character-generation';
  readerAge?: number;
  storyFormat?: string;
  existingContent?: string;
  title?: string;
  storyStage?: string;
  storyTheme?: string;
  storyGenre?: string;
}) => {
  try {
    if (isBlocked()) {
      toast.error(`Content safety: you are temporarily blocked (${getRemainingBlockMinutes()} min left).`);
      throw new Error('Blocked');
    }
    const response = await fetch('/api/story/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const errData = await response.json().catch(() => ({}));
      if (errData?.error) toast.warning(errData.error);
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(errData.error || 'Failed to generate story content');
    }

    const data = await response.json();
    return data.content;
  } catch (error) {
    console.error('Error calling story API:', error);
    throw error;
  }
};

/**
 * Generate a story idea
 */
export const generateStoryIdea = async (readerAge: number = 8, storyFormat: string = 'adventure', title?: string, storyTheme?: string, storyGenre?: string): Promise<string> => {
  return callStoryAPI({
    prompt: 'Generate a creative and engaging story idea that will inspire young writers',
    type: 'story-idea',
    readerAge,
    storyFormat,
    title,
    storyTheme,
    storyGenre
  });
};

/**
 * Continue a story based on existing content
 */
export const continueStory = async (
  existingContent: string, 
  readerAge: number = 8, 
  storyFormat: string = 'adventure',
  title?: string,
  storyStage: string = 'middle',
  storyTheme?: string,
  storyGenre?: string
): Promise<string> => {
  if (!existingContent?.trim()) {
    throw new Error('Story content is required for continuation');
  }

  return callStoryAPI({
    prompt: 'Continue this story in a natural and engaging way, maintaining consistency with the existing narrative and characters. Add new developments that move the story forward while keeping it appropriate and entertaining for children.',
    type: 'story-continuation',
    existingContent,
    readerAge,
    storyFormat,
    title,
    storyStage,
    storyTheme,
    storyGenre
  });
};

/**
 * Generate a specific story continuation option
 */
export const generateStoryOption = async (
  existingContent: string, 
  storyStage: string, 
  readerAge: number = 8, 
  storyFormat: string = 'adventure',
  title?: string,
  storyTheme?: string,
  storyGenre?: string
): Promise<string> => {
  return callStoryAPI({
    prompt: `Generate a ${storyStage} story continuation as a single sentence of no less than 8 words and no more than 20 words for children. Return only one sentence without bullet points or numbering.`,
    type: 'story-continuation',
    existingContent,
    readerAge,
    storyFormat,
    title,
    storyStage,
    storyTheme,
    storyGenre
  });
};

/**
 * Generate a specific plot development suggestion with a particular category focus
 * Categories: 'action', 'discovery', 'character'
 */
export const suggestPlotDevelopment = async (
  currentStory: string, 
  category?: string, 
  readerAge: number = 8, 
  storyFormat: string = 'adventure',
  title?: string,
  storyTheme?: string,
  storyGenre?: string
): Promise<string> => {
  return callStoryAPI({
    prompt: `Suggest an interesting ${category || ''} plot development that could happen next in this story. Make it child-friendly, engaging, and about 2-3 sentences long.`,
    type: 'story-continuation',
    existingContent: currentStory,
    readerAge,
    storyFormat,
    title,
    storyTheme,
    storyGenre
  });
};

/**
 * Generate story themes or writing prompts
 */
export const generateStoryTheme = async (
  readerAge: number = 8, 
  storyFormat: string = 'adventure',
  title?: string,
  storyGenre?: string
): Promise<string> => {
  return callStoryAPI({
    prompt: 'Generate an engaging story theme or writing prompt suitable for children',
    type: 'story-idea',
    readerAge,
    storyFormat,
    title,
    storyGenre
  });
};

/**
 * Generate character ideas for stories
 */
export const generateCharacter = async (
  readerAge: number = 8,
  storyFormat?: string,
  title?: string,
  storyTheme?: string,
  storyGenre?: string
): Promise<string> => {
  return callStoryAPI({
    prompt: 'Generate a unique, child-friendly character for a story with personality traits, goals, and interesting characteristics',
    type: 'character-generation',
    readerAge,
    storyFormat,
    title,
    storyTheme,
    storyGenre
  });
};

/**
 * Generate age-appropriate story suggestions
 */
export const generateAgeAppropriateContent = async (
  prompt: string, 
  readerAge: number, 
  type: 'story-idea' | 'story-option' | 'character-generation'
): Promise<string> => {
  // Get reading level description based on age
  let readingLevel = "";
  switch (readerAge) {
    case 5: readingLevel = "kindergarten level with very simple words and short sentences"; break;
    case 6: readingLevel = "1st grade level with simple words and short sentences"; break;
    case 7: readingLevel = "2nd grade level with basic vocabulary"; break;
    case 8: readingLevel = "3rd grade level with moderate vocabulary"; break;
    case 9: readingLevel = "4th grade level with expanded vocabulary"; break;
    case 10: readingLevel = "5th grade level with more complex vocabulary"; break;
    case 11:
    case 12: readingLevel = "middle school level with diverse vocabulary"; break;
    default: readingLevel = "teen level with advanced vocabulary";
  }
  
  return callCustomStoryAI({
    prompt: `${prompt} Write at a ${readingLevel} reading level.`,
    type: type,
  });
};
