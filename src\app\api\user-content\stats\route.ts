import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createServerSupabaseClient } from '@/lib/supabase/server';

// GET /api/user-content/stats - Get user's content statistics
export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = await createServerSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Authentication required' 
        },
        { status: 401 }
      );
    }

    // Get content statistics by type
    const contentStats = await prisma.userContent.groupBy({
      by: ['type'],
      where: {
        user_id: user.id,
        type: {
          in: ['story', 'art', 'music'] // Only count creative content
        }
      },
      _count: {
        id: true
      }
    });

    // Get total count
    const totalCount = await prisma.userContent.count({
      where: {
        user_id: user.id,
        type: {
          in: ['story', 'art', 'music']
        }
      }
    });

    // Get challenge completions count
    const challengeCompletions = await prisma.challengeCompletion.count({
      where: {
        user_id: user.id
      }
    });

    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentActivity = await prisma.userContent.count({
      where: {
        user_id: user.id,
        type: {
          in: ['story', 'art', 'music']
        },
        created_at: {
          gte: sevenDaysAgo
        }
      }
    });

    // Format statistics
    const stats = {
      stories: contentStats.find(stat => stat.type === 'story')?._count.id || 0,
      artwork: contentStats.find(stat => stat.type === 'art')?._count.id || 0,
      music: contentStats.find(stat => stat.type === 'music')?._count.id || 0,
      total: totalCount,
      challengeCompletions,
      recentActivity
    };

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('Error fetching user content statistics:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch statistics' 
      },
      { status: 500 }
    );
  }
}
