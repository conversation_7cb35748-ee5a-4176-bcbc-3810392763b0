import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { BookOpen, Palette, Music, Calendar, Award, ExternalLink, Loader } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import ProjectViewer from "./ProjectViewer";

interface UserContent {
    id: string;
    type: string;
    title: string;
    content_metadata: any;
    preview_url: string | null;
    challenge_id: string | null;
    created_at: string;
    updated_at: string;
    challenge?: {
        id: string;
        title: string;
        type: string;
        difficulty: string;
    };
}

interface ProjectsGridProps {
    projects: UserContent[];
    isLoading: boolean;
    selectedFilter: string;
}

const ProjectsGrid: React.FC<ProjectsGridProps> = ({
    projects,
    isLoading,
    selectedFilter
}) => {
    const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
    const getTypeIcon = (type: string) => {
        switch (type) {
            case "story":
                return <BookOpen className="h-5 w-5 text-blue-600" />;
            case "art":
                return <Palette className="h-5 w-5 text-green-600" />;
            case "music":
                return <Music className="h-5 w-5 text-purple-600" />;
            default:
                return <BookOpen className="h-5 w-5 text-gray-600" />;
        }
    };

    const getTypeColor = (type: string) => {
        switch (type) {
            case "story":
                return "bg-blue-100 text-blue-800";
            case "art":
                return "bg-green-100 text-green-800";
            case "music":
                return "bg-purple-100 text-purple-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const getDifficultyColor = (difficulty: string) => {
        switch (difficulty) {
            case "easy":
                return "bg-green-100 text-green-800";
            case "medium":
                return "bg-yellow-100 text-yellow-800";
            case "hard":
                return "bg-red-100 text-red-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const handleViewProject = (project: UserContent) => {
        setSelectedProjectId(project.id);
    };

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {Array.from({ length: 6 }, (_, index) => (
                    <Card key={index} className="p-6 animate-pulse">
                        <div className="flex items-center gap-3 mb-4">
                            <div className="w-10 h-10 bg-gray-300 rounded-lg"></div>
                            <div className="flex-1">
                                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                            </div>
                        </div>
                        <div className="h-32 bg-gray-300 rounded mb-4"></div>
                        <div className="h-4 bg-gray-200 rounded mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                    </Card>
                ))}
            </div>
        );
    }

    if (projects.length === 0) {
        const filterMessages = {
            all: "You haven't created any projects yet",
            story: "You haven't created any stories yet",
            art: "You haven't created any artwork yet",
            music: "You haven't created any music yet"
        };

        return (
            <div className="bg-white rounded-xl shadow-sm p-12 text-center mb-8">
                <div className="max-w-md mx-auto">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        {getTypeIcon(selectedFilter === "all" ? "story" : selectedFilter === "art" ? "art" : selectedFilter)}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {filterMessages[selectedFilter as keyof typeof filterMessages]}
                    </h3>
                    <p className="text-gray-600 mb-6">
                        Start creating amazing content to see it here!
                    </p>
                    <Button 
                        onClick={() => window.location.href = "/dashboard"}
                        className="bg-orange-500 hover:bg-orange-600 text-white"
                    >
                        Start Creating
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {projects.map((project) => (
                <Card key={project.id} className="p-6 hover:shadow-lg transition-shadow duration-200">
                    {/* Project Header */}
                    <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                            {getTypeIcon(project.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 truncate">
                                {project.title}
                            </h3>
                            <div className="flex items-center gap-2 mt-1">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(project.type)}`}>
                                    {project.type.charAt(0).toUpperCase() + project.type.slice(1)}
                                </span>
                                {project.challenge && (
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(project.challenge.difficulty)}`}>
                                        <Award className="h-3 w-3 inline mr-1" />
                                        Challenge
                                    </span>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Project Preview */}
                    <div className="mb-4">
                        {project.preview_url ? (
                            <div className="w-full h-32 bg-gray-100 rounded-lg overflow-hidden">
                                {project.type === "art" ? (
                                    <img 
                                        src={project.preview_url} 
                                        alt={project.title}
                                        className="w-full h-full object-cover"
                                    />
                                ) : project.type === "music" ? (
                                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-purple-100 to-purple-200">
                                        <Music className="h-12 w-12 text-purple-600" />
                                    </div>
                                ) : (
                                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200">
                                        <BookOpen className="h-12 w-12 text-blue-600" />
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
                                {getTypeIcon(project.type)}
                            </div>
                        )}
                    </div>

                    {/* Project Info */}
                    <div className="space-y-2 mb-4">
                        {project.challenge && (
                            <p className="text-sm text-gray-600">
                                <Award className="h-4 w-4 inline mr-1" />
                                From: {project.challenge.title}
                            </p>
                        )}
                        <p className="text-sm text-gray-500 flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Created {formatDistanceToNow(new Date(project.created_at), { addSuffix: true })}
                        </p>
                    </div>

                    {/* Actions */}
                    <Button
                        onClick={() => handleViewProject(project)}
                        variant="outline"
                        className="w-full"
                    >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Project
                    </Button>
                </Card>
            ))}

            {/* Project Viewer Modal */}
            <ProjectViewer
                projectId={selectedProjectId}
                onClose={() => setSelectedProjectId(null)}
            />
        </div>
    );
};

export default ProjectsGrid;
