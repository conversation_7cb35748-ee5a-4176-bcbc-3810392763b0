import { NextResponse } from 'next/server';
import { moderateContent } from '@/utils/ai/contentModeration';
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.5-flash",
  temperature: 0.7, 
  apiKey: process.env.GOOGLE_API_KEY || "",
});

export async function POST(request: Request) {
  try {
    const { prompt, style = 'cartoon' } = await request.json();

    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const inCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!inCheck.isAppropriate) {
      return NextResponse.json({ success: false, error: inCheck.reason, blocked: inCheck.blocked ?? false }, { status: 400 });
    }
    
    console.log('=== ART IMPROVE PROMPT API ===');
    
    if (!prompt) {
      console.error('No prompt provided');
      return NextResponse.json(
        { success: false, error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // if (!EURI_API_KEY) {
    //   console.error('EURI_API_KEY not configured');
    //   return NextResponse.json(
    //     { success: false, error: 'Euri API key not configured' },
    //     { status: 500 }
    //   );
    // }

    const improvePrompt = `You are an expert art prompt enhancer for children's artwork. Take the following basic prompt and improve it to create a more vivid, child-friendly ${style} style description. Keep the final result within 25 words.

IMPORTANT GUIDELINES:
1. Keep content strictly child-friendly and positive
2. Add vivid colors, textures, and visual details
3. Make descriptions engaging and imaginative
4. Focus on themes of wonder, magic, and adventure
5. Avoid any scary, violent, or inappropriate content
6. Use clear, descriptive language
7. Keep the core concept but make it more detailed and artistic
8. Limit the result to 25 words maximum

Original prompt: "${prompt}"

Please provide an improved, more detailed version of this prompt that would create better artwork:`;

    console.log('Calling Euri API...');
    
    const response = await llm.invoke([
      {
        role: "user",
        content: improvePrompt
      }
    ]);

    const initialImprovedPrompt = String(response.content);

    const outCheck = await moderateContent(initialImprovedPrompt, 'text');
    const improvedPrompt = outCheck.isAppropriate ? initialImprovedPrompt : "Let's think of a different artistic idea!";
    console.log('Improved prompt:', improvedPrompt);

    return NextResponse.json({
      success: true,
      improvedPrompt,
      originalPrompt: prompt
    });

  } catch (error) {
    console.error('Error improving prompt:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to improve prompt' },
      { status: 500 }
    );
  }
} 



