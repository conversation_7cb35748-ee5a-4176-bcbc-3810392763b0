import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { stripe, getPlanIdFromPriceId, getBillingCycleFromPlanId, getPlanDisplayName, calculateBillingPeriod, type StripeSubscriptionWithPeriods } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';
import type <PERSON><PERSON> from 'stripe';

// Extended Stripe subscription type with missing properties
interface StripeInvoiceWithSubscription extends Stripe.Invoice {
  subscription?: string | null;
}

export async function POST(request: NextRequest) {
  console.log('🔔 Webhook received!');
  
  if (!stripe) {
    console.error('❌ Stripe is not configured');
    return NextResponse.json(
      { error: 'Stripe is not configured' },
      { status: 500 }
    );
  }

  const body = await request.text();
  const headersList = await headers();
  const signature = headersList.get('stripe-signature');

  console.log('📝 Webhook signature present:', !!signature);

  if (!signature) {
    console.error('❌ Missing stripe-signature header');
    return NextResponse.json(
      { error: 'Missing stripe-signature header' },
      { status: 400 }
    );
  }

  // For local development, use endpoint secret from Stripe CLI
  // For production, use the webhook secret from Stripe dashboard
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || process.env.STRIPE_ENDPOINT_SECRET;
  console.log('🔑 Webhook secret configured:', !!webhookSecret);
  
  if (!webhookSecret) {
    console.error('❌ Webhook secret not configured - skipping signature verification for development');
    console.log('💡 For local testing, either:');
    console.log('   1. Set STRIPE_ENDPOINT_SECRET from Stripe CLI output');
    console.log('   2. Set STRIPE_WEBHOOK_SECRET from Stripe dashboard');
    
    // For development, we'll try to parse the event without verification
    let event;
    try {
      event = JSON.parse(body);
      console.log('📦 Parsed event without verification:', event.type);
    } catch (err) {
      console.error('❌ Failed to parse webhook body:', err);
      return NextResponse.json(
        { error: 'Invalid webhook body' },
        { status: 400 }
      );
    }
    
    // Process the event
    await processWebhookEvent(event);
    return NextResponse.json({ received: true });
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      webhookSecret
    );
  } catch (err) {
    console.error('Webhook signature verification failed.', err);
    return NextResponse.json(
      { error: 'Webhook signature verification failed' },
      { status: 400 }
    );
  }

  // Handle the event with signature verification
  await processWebhookEvent(event);
  return NextResponse.json({ received: true });
}

// Extract event processing into a separate function
async function processWebhookEvent(event: Stripe.Event) {
  console.log('🎯 Processing webhook event:', event.type);
  
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('💰 Payment succeeded:', paymentIntent.id);
      await handleSuccessfulPayment(paymentIntent);
      break;

    case 'invoice.payment_succeeded':
      const invoice = event.data.object;
      console.log('🧾 Invoice payment succeeded:', invoice.id);
      await handleInvoicePaymentSucceeded(invoice);
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('❌ Payment failed:', failedPayment.id);
      
      // Handle failed payment
      await handleFailedPayment(failedPayment);
      break;

    case 'customer.subscription.created':
      const subscription = event.data.object;
      console.log('🔄 Subscription created:', subscription.id);
      
      // Handle new subscription
      await handleSubscriptionCreated(subscription as StripeSubscriptionWithPeriods);
      break;

    case 'customer.subscription.updated':
      const updatedSubscription = event.data.object;
      console.log('🔄 Subscription updated:', updatedSubscription.id);
      
      // TEMPORARILY DISABLED - Don't override manual active status
      console.log('⚠️ WEBHOOK DISABLED - Subscription update handler temporarily disabled to prevent status override');
      // await handleSubscriptionUpdated(updatedSubscription as StripeSubscriptionWithPeriods);
      break;

    case 'customer.subscription.deleted':
      const deletedSubscription = event.data.object;
      console.log('🗑️ Subscription deleted:', deletedSubscription.id);
      
      // Handle subscription cancellation
      await handleSubscriptionDeleted(deletedSubscription);
      break;

    case 'setup_intent.succeeded':
      const setupIntent = event.data.object;
      console.log('⚙️ Setup intent succeeded:', setupIntent.id);
      
      // Handle successful trial setup
      await handleSetupIntentSucceeded(setupIntent);
      break;

    case 'payment_method.attached':
      const paymentMethod = event.data.object;
      console.log('💳 Payment method attached:', paymentMethod.id);
      // This event is informational - no action needed
      break;

    case 'invoice.created':
      const createdInvoice = event.data.object as StripeInvoiceWithSubscription;
      console.log('📋 Invoice created:', createdInvoice.id);
      console.log('📋 Invoice subscription ID:', createdInvoice.subscription);
      await handleInvoiceCreated(createdInvoice);
      break;

    case 'invoice.finalized':
      const finalizedInvoice = event.data.object as StripeInvoiceWithSubscription;
      console.log('📄 Invoice finalized:', finalizedInvoice.id);
      console.log('📄 Invoice subscription ID:', finalizedInvoice.subscription);
      await handleInvoiceFinalized(finalizedInvoice);
      break;

    case 'invoice.paid':
      const paidInvoice = event.data.object as StripeInvoiceWithSubscription;
      console.log('💵 Invoice paid:', paidInvoice.id);
      console.log('💵 Invoice subscription ID:', paidInvoice.subscription);
      await handleInvoicePaid(paidInvoice);
      break;

    case 'setup_intent.created':
      const createdSetupIntent = event.data.object;
      console.log('🔧 Setup intent created:', createdSetupIntent.id);
      await handleSetupIntentCreated(createdSetupIntent);
      break;

    case 'customer.subscription.trial_will_end':
      const trialEndingSubscription = event.data.object;
      console.log('⏰ Trial will end for subscription:', trialEndingSubscription.id);
      await handleTrialWillEnd(trialEndingSubscription);
      break;

    case 'invoice.payment_failed':
      const failedInvoice = event.data.object as StripeInvoiceWithSubscription;
      console.log('❌ Invoice payment failed:', failedInvoice.id);
      console.log('❌ Invoice subscription ID:', failedInvoice.subscription);
      await handleInvoicePaymentFailed(failedInvoice);
      break;

    case 'invoice.payment_action_required':
      const actionRequiredInvoice = event.data.object as StripeInvoiceWithSubscription;
      console.log('🔐 Invoice payment action required:', actionRequiredInvoice.id);
      console.log('🔐 Invoice subscription ID:', actionRequiredInvoice.subscription);
      
      // TEMPORARILY DISABLED - Don't override manual active status
      console.log('⚠️ WEBHOOK DISABLED - Invoice payment action required handler temporarily disabled');
      // await handleInvoicePaymentActionRequired(actionRequiredInvoice);
      break;

    case 'customer.subscription.paused':
      const pausedSubscription = event.data.object;
      console.log('⏸️ Subscription paused:', pausedSubscription.id);
      await handleSubscriptionPaused(pausedSubscription);
      break;

    case 'customer.subscription.resumed':
      const resumedSubscription = event.data.object;
      console.log('▶️ Subscription resumed:', resumedSubscription.id);
      await handleSubscriptionResumed(resumedSubscription);
      break;

    case 'customer.created':
      const customer = event.data.object;
      console.log('👤 Customer created:', customer.id);
      await handleCustomerCreated(customer);
      break;

    case 'customer.updated':
      const updatedCustomer = event.data.object;
      console.log('👤 Customer updated:', updatedCustomer.id);
      await handleCustomerUpdated(updatedCustomer);
      break;

    case 'customer.deleted':
      const deletedCustomer = event.data.object;
      console.log('🗑️ Customer deleted:', deletedCustomer.id);
      await handleCustomerDeleted(deletedCustomer);
      break;

    case 'setup_intent.setup_failed':
      const failedSetupIntent = event.data.object;
      console.log('❌ Setup intent failed:', failedSetupIntent.id);
      await handleSetupIntentFailed(failedSetupIntent);
      break;

    case 'payment_method.detached':
      const detachedPaymentMethod = event.data.object;
      console.log('🔓 Payment method detached:', detachedPaymentMethod.id);
      await handlePaymentMethodDetached(detachedPaymentMethod);
      break;

    case 'invoice.upcoming':
      const upcomingInvoice = event.data.object as StripeInvoiceWithSubscription;
      console.log('📅 Upcoming invoice:', upcomingInvoice.id);
      console.log('📅 Invoice subscription ID:', upcomingInvoice.subscription);
      await handleUpcomingInvoice(upcomingInvoice);
      break;

    case 'customer.subscription.pending_update_applied':
      const updatedPendingSubscription = event.data.object as StripeSubscriptionWithPeriods;
      console.log('🔄 Subscription pending update applied:', updatedPendingSubscription.id);
      await handleSubscriptionPendingUpdateApplied(updatedPendingSubscription);
      break;

    case 'customer.subscription.pending_update_expired':
      const expiredPendingSubscription = event.data.object;
      console.log('⌛ Subscription pending update expired:', expiredPendingSubscription.id);
      await handleSubscriptionPendingUpdateExpired(expiredPendingSubscription);
      break;

    default:
      console.log(`⚠️ Unhandled event type ${event.type}`);
  }
}

// Helper functions to handle different events
async function handleSuccessfulPayment(paymentIntent: Stripe.PaymentIntent) {
  try {
    const { planId, email, customerName } = paymentIntent.metadata || {};
    
    if (!email) {
      console.error('No email found in payment intent metadata');
      return;
    }

    // Find user by email
    const profile = await prisma.profile.findUnique({
      where: { email }
    });

    if (!profile) {
      console.error('Profile not found for email:', email);
      return;
    }

    // Create payment record
    console.log('Creating payment record:', {
      profileId: profile.id,
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount / 100,
      planId
    });

    await prisma.payment.create({
      data: {
        profile_id: profile.id,
        stripe_payment_id: paymentIntent.id,
        amount: paymentIntent.amount / 100,
        currency: paymentIntent.currency,
        status: 'succeeded',
        plan_id: planId || null,
        plan_name: getPlanDisplayName(planId || ''),
        payment_date: new Date()
      }
    });

    // Update Stripe customer ID if not set
    if (!profile.stripe_customer_id && paymentIntent.customer) {
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          stripe_customer_id: paymentIntent.customer as string
        }
      });
    }
    
    console.log('Successfully processed payment for:', {
      planId,
      email,
      customerName,
      amount: paymentIntent.amount / 100,
    });
    
  } catch (error) {
    console.error('Error handling successful payment:', error);
  }
}

async function handleFailedPayment(paymentIntent: Stripe.PaymentIntent) {
  try {
    const { email } = paymentIntent.metadata || {};
    
    // TODO: Implement your business logic here
    // Examples:
    // - Send payment failed email
    // - Log failed payment attempt
    // - Notify admin of failed payment
    
    console.log('Processing failed payment for:', email);
    
  } catch (error) {
    console.error('Error handling failed payment:', error);
  }
}

async function handleSubscriptionCreated(subscription: StripeSubscriptionWithPeriods) {
  try {
    console.log('Processing subscription creation for customer:', subscription.customer);
    
    // Find profile by customer ID
    const profile = await prisma.profile.findFirst({
      where: { stripe_customer_id: subscription.customer as string }
    });

    if (!profile) {
      console.error('Profile not found for customer:', subscription.customer);
      return;
    }

    // Extract plan information from subscription
    const priceId = subscription.items.data[0]?.price?.id;
    const planId = getPlanIdFromPriceId(priceId);

    if (!planId) {
      console.error('Could not determine plan ID from price ID:', priceId);
      return;
    }

    console.log('Creating new subscription:', {
      profileId: profile.id,
      subscriptionId: subscription.id,
      planId,
      status: subscription.status
    });

    // Calculate billing periods using centralized logic
    const { periodStart, periodEnd } = calculateBillingPeriod(subscription, planId);
    const billingCycle = getBillingCycleFromPlanId(planId);

    console.log('📅 Calculated billing period:', {
      start: new Date(periodStart * 1000).toISOString(),
      end: new Date(periodEnd * 1000).toISOString(),
      cycle: billingCycle,
      planId
    });

    const updateData = {
      subscription_id: subscription.id,
      subscription_status: subscription.status,
      plan_id: planId,
      plan_name: getPlanDisplayName(planId),
      billing_cycle: billingCycle,
      subscription_start: new Date(periodStart * 1000),
      subscription_end: new Date(periodEnd * 1000),
      trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
      trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
      updated_at: new Date()
    };

    await prisma.profile.update({
      where: { id: profile.id },
      data: updateData
    });

    console.log('✅ New subscription created and saved successfully:', subscription.id);
    console.log('📊 Subscription details:', {
      planId,
      billingCycle,
      start: updateData.subscription_start.toISOString(),
      end: updateData.subscription_end.toISOString()
    });
  } catch (error) {
    console.error('Error handling subscription creation:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }
  }
}

// async function handleSubscriptionUpdated(subscription: StripeSubscriptionWithPeriods) {
//   try {
//     // Find profile by subscription ID
//     const profile = await prisma.profile.findFirst({
//       where: { subscription_id: subscription.id }
//     });

//     if (!profile) {
//       console.error('Profile not found for subscription:', subscription.id);
//       return;
//     }

//     // Extract plan information from subscription
//     const priceId = subscription.items.data[0]?.price?.id;
//     const planId = getPlanIdFromPriceId(priceId);

//     if (!planId) {
//       console.error('Could not determine plan ID from price ID:', priceId);
//       return;
//     }

//     console.log('Updating subscription:', {
//       subscriptionId: subscription.id,
//       newStatus: subscription.status,
//       previousStatus: profile.subscription_status,
//       planId: planId
//     });

//     // Check for important status transitions
//     if (profile.subscription_status === 'trialing' && subscription.status === 'active') {
//       console.log('🎉 TRIAL TO PAID CONVERSION SUCCESSFUL!');
//       console.log('✅ Customer was automatically charged after trial ended');
//       console.log('🚀 Subscription is now active and recurring billing will continue');
      
//       // Mark trial as used when trial converts to paid
//       await prisma.profile.update({
//         where: { id: profile.id },
//         data: {
//           trial_used: true,
//           updated_at: new Date()
//         }
//       });
//     }

//     if (profile.subscription_status === 'trialing' && subscription.status === 'past_due') {
//       console.log('❌ TRIAL TO PAID CONVERSION FAILED!');
//       console.log('💳 Payment failed when trial ended - customer needs to update payment method');
//       console.log('🔄 Stripe will retry payment according to your retry settings');
      
//       // Mark trial as used even if payment failed
//       await prisma.profile.update({
//         where: { id: profile.id },
//         data: {
//           trial_used: true,
//           updated_at: new Date()
//         }
//       });
//     }

//     // Calculate billing periods using centralized logic
//     const { periodStart, periodEnd } = calculateBillingPeriod(subscription, planId);
//     const billingCycle = getBillingCycleFromPlanId(planId);

//     console.log('📅 Calculated billing period:', {
//       start: new Date(periodStart * 1000).toISOString(),
//       end: new Date(periodEnd * 1000).toISOString(),
//       cycle: billingCycle,
//       planId
//     });

//     // Handle cancel_at_period_end status
//     const stripeStatus = (subscription as Stripe.Subscription & { cancel_at_period_end?: boolean }).cancel_at_period_end ? 'cancel_at_period_end' : subscription.status;
    
//     // Don't override manually set 'active' status with 'incomplete' from Stripe
//     const finalStatus = (profile.subscription_status === 'active' && stripeStatus === 'incomplete') 
//       ? 'active' 
//       : stripeStatus;

//     console.log('📊 Status decision:', {
//       stripeStatus,
//       currentDbStatus: profile.subscription_status,
//       finalStatus
//     });

//     const updateData = {
//       subscription_status: finalStatus,
//       plan_id: planId,
//       plan_name: getPlanDisplayName(planId),
//       billing_cycle: billingCycle,
//       subscription_start: new Date(periodStart * 1000),
//       subscription_end: new Date(periodEnd * 1000),
//       trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
//       trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
//       updated_at: new Date()
//     };

//     console.log('📊 Database update data:', updateData);

//     const updatedProfile = await prisma.profile.update({
//       where: { id: profile.id },
//       data: updateData
//     });

//     console.log('✅ Subscription updated in database:', subscription.id);
//     console.log('📈 Final subscription status:', updatedProfile.subscription_status);
//   } catch (error) {
//     console.error('Error handling subscription update:', error);
//   }
// }

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  try {
    // Find profile by subscription ID
    const profile = await prisma.profile.findFirst({
      where: { subscription_id: subscription.id }
    });

    if (profile) {
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          subscription_status: 'canceled',
          subscription_end: new Date(),
          trial_used: true, // Mark trial as used when subscription is canceled
          updated_at: new Date()
        }
      });
    }

    console.log('Subscription deleted:', subscription.id);
  } catch (error) {
    console.error('Error handling subscription deletion:', error);
  }
}

async function handleSetupIntentSucceeded(setupIntent: Stripe.SetupIntent) {
  try {
    const { email, planId, customerName, trial_subscription } = setupIntent.metadata || {};
    
    if (!email) {
      console.error('No email found in setup intent metadata');
      return;
    }

    // Find profile by email
    const profile = await prisma.profile.findUnique({
      where: { email }
    });

    if (!profile) {
      console.error('Profile not found for email:', email);
      return;
    }

    // Update Stripe customer ID if available
    if (setupIntent.customer && !profile.stripe_customer_id) {
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          stripe_customer_id: setupIntent.customer as string,
          updated_at: new Date()
        }
      });
      console.log('Updated profile with customer ID:', setupIntent.customer);
    }

    // If this is for a trial subscription, set the payment method as default
    if (trial_subscription === 'true' && setupIntent.payment_method && setupIntent.customer && stripe) {
      try {
        console.log('Setting up payment method for trial subscription:', {
          paymentMethodId: setupIntent.payment_method,
          customerId: setupIntent.customer
        });

        // Set the payment method as the default for the customer
        await stripe.customers.update(setupIntent.customer as string, {
          invoice_settings: {
            default_payment_method: setupIntent.payment_method as string,
          },
        });

        console.log('✅ Payment method set as default for invoice settings');

        console.log('✅ Payment method set as default for automatic charging');
        console.log('🎯 Trial will automatically charge when it ends');

      } catch (paymentMethodError) {
        console.error('Error setting default payment method:', paymentMethodError);
        // Don't fail the entire process if this fails
      }
    }

    console.log('Setup intent processed successfully:', {
      setupIntentId: setupIntent.id,
      email,
      planId,
      customerName,
      isTrialSubscription: trial_subscription === 'true'
    });
  } catch (error) {
    console.error('Error handling setup intent:', error);
  }
}

// Handle invoice payment succeeded (for subscriptions)
async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice & { subscription?: string; payment_intent?: string }) {
  try {
    console.log('Processing invoice payment succeeded:', {
      invoiceId: invoice.id,
      subscriptionId: invoice.subscription,
      amountPaid: invoice.amount_paid,
      customerId: invoice.customer
    });

    // Find profile by customer ID (works for both subscription and trial invoices)
    const profile = await prisma.profile.findFirst({
      where: { 
        OR: [
          { subscription_id: invoice.subscription as string },
          { stripe_customer_id: invoice.customer as string }
        ]
      }
    });

    if (!profile) {
      console.error('Profile not found for invoice:', invoice.id);
      return;
    }

    // Only create payment record if amount was actually paid (not $0 trial invoices)
    if (invoice.amount_paid && invoice.amount_paid > 0) {
      let planId: string | null = null;
      
      // Try to get plan information from subscription if available
      if (stripe && invoice.subscription) {
        try {
          const subscription = await stripe.subscriptions.retrieve(invoice.subscription as string);
          const priceId = subscription.items.data[0]?.price?.id;
          planId = getPlanIdFromPriceId(priceId);
        } catch (error) {
          console.warn('Could not retrieve subscription details:', error);
        }
      }

      // If no subscription, try to get plan from profile
      if (!planId && profile.plan_id) {
        planId = profile.plan_id;
      }

      // Create payment record
      const paymentId = (invoice.payment_intent as string) || invoice.id || `invoice_${Date.now()}`;
      
      console.log('Creating payment record for invoice:', {
        profileId: profile.id,
        paymentId,
        amount: invoice.amount_paid / 100,
        planId
      });

      await prisma.payment.create({
        data: {
          profile_id: profile.id,
          stripe_payment_id: paymentId,
          amount: invoice.amount_paid / 100,
          currency: invoice.currency || 'usd',
          status: 'succeeded',
          plan_id: planId,
          plan_name: getPlanDisplayName(planId || ''),
          payment_date: new Date()
        }
      });

      console.log('💰 Payment record created successfully for invoice:', invoice.id);
      console.log('💳 Amount paid:', invoice.amount_paid / 100, invoice.currency?.toUpperCase());
    } else {
      console.log('📝 Invoice paid but amount is $0 (likely trial invoice) - no payment record created');
    }

    console.log('Invoice payment processed successfully:', invoice.id);
  } catch (error) {
    console.error('Error handling invoice payment:', error);
  }
}

// Handle invoice created event
async function handleInvoiceCreated(invoice: StripeInvoiceWithSubscription) {
  try {
    console.log('Processing invoice creation:', {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      subscriptionId: invoice.subscription,
      status: invoice.status
    });
    
    // If this is for a subscription, update any relevant data
    if (invoice.subscription) {
      console.log('Invoice is for subscription:', invoice.subscription);
    }
    
    // This is primarily informational - no specific action needed
    // Could be used for logging, analytics, or notifications
    
  } catch (error) {
    console.error('Error handling invoice creation:', error);
  }
}

// Handle invoice finalized event
async function handleInvoiceFinalized(invoice: StripeInvoiceWithSubscription) {
  try {
    console.log('Processing invoice finalization:', {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      subscriptionId: invoice.subscription,
      total: invoice.total
    });
    
    // Invoice has been finalized and is ready to be sent to customer
    // This could be used to trigger email notifications or update records
    
  } catch (error) {
    console.error('Error handling invoice finalization:', error);
  }
}

// Handle invoice paid event
async function handleInvoicePaid(invoice: StripeInvoiceWithSubscription) {
  try {
    console.log('Processing invoice paid:', {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      subscriptionId: invoice.subscription,
      amountPaid: invoice.amount_paid
    });
    
    // Invoice has been paid successfully
    // This is similar to invoice.payment_succeeded but more specific
    // Could be used for additional business logic or notifications
    
  } catch (error) {
    console.error('Error handling invoice paid:', error);
  }
}

// Handle setup intent created event
async function handleSetupIntentCreated(setupIntent: Stripe.SetupIntent) {
  try {
    console.log('Processing setup intent creation:', {
      setupIntentId: setupIntent.id,
      customerId: setupIntent.customer,
      status: setupIntent.status
    });
    
    // Setup intent has been created for future payments
    // This is primarily informational - main logic happens on setup_intent.succeeded
    
  } catch (error) {
    console.error('Error handling setup intent creation:', error);
  }
}

// Handle trial will end event
async function handleTrialWillEnd(subscription: Stripe.Subscription) {
  try {
    console.log('Processing trial ending notification:', {
      subscriptionId: subscription.id,
      customerId: subscription.customer,
      trialEnd: subscription.trial_end
    });
    
    // Find profile by subscription ID
    const profile = await prisma.profile.findFirst({
      where: { subscription_id: subscription.id }
    });

    if (!profile) {
      console.error('Profile not found for subscription:', subscription.id);
      return;
    }

    // Could send email notification about trial ending
    // Could update user preferences or show in-app notifications
    console.log(`Trial will end for user ${profile.email} on ${subscription.trial_end ? new Date(subscription.trial_end * 1000) : 'unknown date'}`);
    
    // Check if customer has a default payment method
    if (stripe) {
      try {
        const customer = await stripe.customers.retrieve(subscription.customer as string);
        if (customer && !customer.deleted) {
          const hasPaymentMethod = customer.default_source || customer.invoice_settings?.default_payment_method;
          console.log(`Customer has default payment method: ${!!hasPaymentMethod}`);
          
          if (!hasPaymentMethod) {
            console.warn('⚠️ Customer has no default payment method - trial will need manual intervention');
            console.warn('💡 This should not happen if setup intent was completed successfully');
          } else {
            console.log('✅ Customer has payment method - automatic charging will proceed');
            console.log('🎯 When trial ends, Stripe will automatically:');
            console.log('   1. Create an invoice for the subscription');
            console.log('   2. Attempt to charge the default payment method');
            console.log('   3. If successful, subscription becomes "active"');
            console.log('   4. If failed, subscription becomes "past_due" and retries begin');
          }
        }
      } catch (error) {
        console.error('Error checking customer payment method:', error);
      }
    }
    
    // Optionally update a field to track trial ending notifications
    // await prisma.profile.update({
    //   where: { id: profile.id },
    //   data: {
    //     trial_ending_notified: true,
    //     updated_at: new Date()
    //   }
    // });
    
  } catch (error) {
    console.error('Error handling trial will end:', error);
  }
}

// Handle invoice payment failed event
async function handleInvoicePaymentFailed(invoice: StripeInvoiceWithSubscription) {
  try {
    console.log('Processing invoice payment failure:', {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      subscriptionId: invoice.subscription,
      attemptCount: invoice.attempt_count
    });
    
    // Find profile by subscription ID or customer
    const profile = await prisma.profile.findFirst({
      where: { 
        OR: [
          { subscription_id: invoice.subscription as string },
          { stripe_customer_id: invoice.customer as string }
        ]
      }
    });

    if (profile) {
      console.log(`Payment failed for user ${profile.email}, attempt ${invoice.attempt_count}`);
      
      // Could update subscription status or send notification
      // Could implement dunning management logic here
      
      // If this is the final attempt, might want to update subscription status
      if (invoice.attempt_count && invoice.attempt_count >= 4) {
        console.log('Final payment attempt failed, subscription may be cancelled');
      }
    }
    
  } catch (error) {
    console.error('Error handling invoice payment failure:', error);
  }
}

// Handle invoice payment action required event
// async function handleInvoicePaymentActionRequired(invoice: StripeInvoiceWithSubscription) {
//   try {
//     console.log('Processing invoice payment action required:', {
//       invoiceId: invoice.id,
//       customerId: invoice.customer,
//       subscriptionId: invoice.subscription,
//       status: invoice.status
//     });
    
//     // Find profile by subscription ID or customer
//     const profile = await prisma.profile.findFirst({
//       where: { 
//         OR: [
//           { subscription_id: invoice.subscription as string },
//           { stripe_customer_id: invoice.customer as string }
//         ]
//       }
//     });

//     if (profile) {
//       console.log(`Payment action required for user ${profile.email} - trial ending without payment method`);
      
//       // Only update to incomplete if not already manually set to active
//       if (profile.subscription_status !== 'active') {
//         await prisma.profile.update({
//           where: { id: profile.id },
//           data: {
//             subscription_status: 'incomplete',
//             updated_at: new Date()
//           }
//         });
//         console.log('📊 Status updated to incomplete for payment action required');
//       } else {
//         console.log('📊 Keeping manually set active status despite payment action required');
//       }
      
//       // TODO: Send email notification to customer to add payment method
//       // This is where you would typically:
//       // 1. Send an email with a link to update payment method
//       // 2. Set up customer portal link
//       // 3. Show in-app notification
//     }
    
//   } catch (error) {
//     console.error('Error handling invoice payment action required:', error);
//   }
// }

// Handle subscription paused event
async function handleSubscriptionPaused(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription pause:', {
      subscriptionId: subscription.id,
      customerId: subscription.customer
    });
    
    // Find profile by subscription ID
    const profile = await prisma.profile.findFirst({
      where: { subscription_id: subscription.id }
    });

    if (profile) {
      // Update subscription status to paused
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          subscription_status: 'paused',
          updated_at: new Date()
        }
      });
      
      console.log(`Subscription paused for user ${profile.email}`);
    }
    
  } catch (error) {
    console.error('Error handling subscription pause:', error);
  }
}

// Handle subscription resumed event
async function handleSubscriptionResumed(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription resume:', {
      subscriptionId: subscription.id,
      customerId: subscription.customer
    });
    
    // Find profile by subscription ID
    const profile = await prisma.profile.findFirst({
      where: { subscription_id: subscription.id }
    });

    if (profile) {
      // Update subscription status to active
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          subscription_status: subscription.status,
          updated_at: new Date()
        }
      });
      
      console.log(`Subscription resumed for user ${profile.email}`);
    }
    
  } catch (error) {
    console.error('Error handling subscription resume:', error);
  }
}

// Handle customer created event
async function handleCustomerCreated(customer: Stripe.Customer) {
  try {
    console.log('Processing customer creation:', {
      customerId: customer.id,
      email: customer.email
    });
    
    // This event is primarily informational
    // The customer is usually already created when handling other events
    // Could be used for analytics or additional customer setup
    
  } catch (error) {
    console.error('Error handling customer creation:', error);
  }
}

// Handle customer updated event
async function handleCustomerUpdated(customer: Stripe.Customer) {
  try {
    console.log('Processing customer update:', {
      customerId: customer.id,
      email: customer.email
    });
    
    // Find profile by customer ID
    const profile = await prisma.profile.findFirst({
      where: { stripe_customer_id: customer.id }
    });

    if (profile && customer.email && customer.email !== profile.email) {
      // Update email if it changed in Stripe
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          email: customer.email,
          updated_at: new Date()
        }
      });
      
      console.log(`Customer email updated: ${profile.email} -> ${customer.email}`);
    }
    
  } catch (error) {
    console.error('Error handling customer update:', error);
  }
}

// Handle customer deleted event
async function handleCustomerDeleted(customer: Stripe.Customer) {
  try {
    console.log('Processing customer deletion:', {
      customerId: customer.id
    });
    
    // Find profile by customer ID
    const profile = await prisma.profile.findFirst({
      where: { stripe_customer_id: customer.id }
    });

    if (profile) {
      // Clear Stripe customer ID and subscription data
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          stripe_customer_id: null,
          subscription_id: null,
          subscription_status: null,
          plan_id: null,
          plan_name: null,
          billing_cycle: null,
          subscription_start: null,
          subscription_end: null,
          trial_end: null,
          updated_at: new Date()
        }
      });
      
      console.log(`Customer data cleared for user ${profile.email}`);
    }
    
  } catch (error) {
    console.error('Error handling customer deletion:', error);
  }
}

// Handle setup intent failed event
async function handleSetupIntentFailed(setupIntent: Stripe.SetupIntent) {
  try {
    console.log('Processing setup intent failure:', {
      setupIntentId: setupIntent.id,
      customerId: setupIntent.customer,
      lastSetupError: setupIntent.last_setup_error
    });
    
    // Setup intent failed - could notify user or log for debugging
    // This might happen if the payment method is invalid or declined
    
  } catch (error) {
    console.error('Error handling setup intent failure:', error);
  }
}

// Handle payment method detached event
async function handlePaymentMethodDetached(paymentMethod: Stripe.PaymentMethod) {
  try {
    console.log('Processing payment method detachment:', {
      paymentMethodId: paymentMethod.id,
      customerId: paymentMethod.customer
    });
    
    // Payment method has been detached from customer
    // This is primarily informational - Stripe handles the detachment
    // Could be used for notifications or analytics
    
  } catch (error) {
    console.error('Error handling payment method detachment:', error);
  }
}

// Handle upcoming invoice event
async function handleUpcomingInvoice(invoice: StripeInvoiceWithSubscription) {
  try {
    console.log('Processing upcoming invoice:', {
      invoiceId: invoice.id,
      customerId: invoice.customer,
      subscriptionId: invoice.subscription,
      periodStart: invoice.period_start,
      periodEnd: invoice.period_end
    });
    
    // Find profile by subscription ID or customer
    const profile = await prisma.profile.findFirst({
      where: { 
        OR: [
          { subscription_id: invoice.subscription as string },
          { stripe_customer_id: invoice.customer as string }
        ]
      }
    });

    if (profile) {
      console.log(`Upcoming invoice for user ${profile.email}`);
      
      // Could send email notification about upcoming charge
      // Could be used for pre-billing notifications
    }
    
  } catch (error) {
    console.error('Error handling upcoming invoice:', error);
  }
}

// Handle subscription pending update applied event
async function handleSubscriptionPendingUpdateApplied(subscription: StripeSubscriptionWithPeriods) {
  try {
    console.log('Processing subscription pending update applied:', {
      subscriptionId: subscription.id,
      customerId: subscription.customer
    });
    
    // A pending subscription update has been applied
    // This happens when subscription changes are scheduled for the next billing cycle
    // and that billing cycle has now started
    
    // Find profile by subscription ID
    const profile = await prisma.profile.findFirst({
      where: { subscription_id: subscription.id }
    });

    if (profile) {
      // Extract plan information from subscription
      const priceId = subscription.items.data[0]?.price?.id;
      const planId = getPlanIdFromPriceId(priceId);

      // Use centralized billing period calculation for correct quarterly periods
      const { periodStart, periodEnd } = calculateBillingPeriod(subscription, planId);
      const billingCycle = getBillingCycleFromPlanId(planId || '');

      // Update subscription data with the new changes
      await prisma.profile.update({
        where: { id: profile.id },
        data: {
          subscription_status: subscription.status,
          plan_id: planId,
          plan_name: getPlanDisplayName(planId || ''),
          billing_cycle: billingCycle,
          subscription_start: new Date(periodStart * 1000),
          subscription_end: new Date(periodEnd * 1000),
          trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
          trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
          updated_at: new Date()
        }
      });

      console.log(`Pending update applied for user ${profile.email}`);
    }
    
  } catch (error) {
    console.error('Error handling subscription pending update applied:', error);
  }
}

// Handle subscription pending update expired event
async function handleSubscriptionPendingUpdateExpired(subscription: Stripe.Subscription) {
  try {
    console.log('Processing subscription pending update expired:', {
      subscriptionId: subscription.id,
      customerId: subscription.customer
    });
    
    // A pending subscription update has expired without being applied
    // This might happen if the subscription was cancelled before the pending update could be applied
    
    // Find profile by subscription ID
    const profile = await prisma.profile.findFirst({
      where: { subscription_id: subscription.id }
    });

    if (profile) {
      console.log(`Pending update expired for user ${profile.email}`);
      
      // Could notify user that their scheduled plan change was cancelled
      // Could be used for analytics or customer support notifications
    }
    
  } catch (error) {
    console.error('Error handling subscription pending update expired:', error);
  }
}