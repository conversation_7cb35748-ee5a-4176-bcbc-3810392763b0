import { NextResponse } from 'next/server';
import { getFormatGuideline } from '@/components/story/helpers/StoryFormatUtils';
import { moderateContent } from '@/utils/ai/contentModeration';
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";

const llm = new ChatGoogleGenerativeAI({
  model: "gemini-2.5-flash",
  temperature: 0.7,
  apiKey: process.env.GOOGLE_API_KEY || "",
});

export async function POST(request: Request) {
  try {
    const {
      prompt,
      readerAge,
      storyFormat,
      type = 'story-idea',
      existingContent = '',
      title = '',
      storyStage = 'middle',
      storyTheme = '',
      storyGenre = ''
    } = await request.json();

    // Content safety check on the user prompt
    const userKey = request.headers.get('x-forwarded-for') ?? 'anonymous';
    const promptCheck = await moderateContent(prompt || '', 'prompt', userKey);
    if (!promptCheck.isAppropriate) {
      return NextResponse.json(
        { success: false, error: promptCheck.reason, blocked: promptCheck.blocked ?? false },
        { status: 400 }
      );
    }
    console.log('prompt', prompt);
    console.log('readerAge', readerAge);
    console.log('storyFormat', storyFormat);
    console.log('type', type);
    console.log('existingContent', existingContent);
    console.log('title', title);
    console.log('storyStage', storyStage);
    console.log('storyTheme', storyTheme);
    console.log('storyGenre', storyGenre);

    // Debug: print OpenAI API key from env
    // console.log('OPENAI_API_KEY:', process.env.OPENAI_API_KEY);

    // Get format-specific guidelines
    const formatGuideline = getFormatGuideline(storyFormat, readerAge);

    // Build context from available fields
    const storyContext = [
      title && `Title: "${title}"`,
      storyTheme && `Theme: ${storyTheme}`,
      storyGenre && `Genre: ${storyGenre}`,
      storyStage && `Current stage of the story: ${storyStage}`,
      existingContent && `Current story content:\n${existingContent}`
    ].filter(Boolean).join('\n');

    // Base prompt template that ensures child-friendly content
    const basePrompt = `You are a creative children's story writer. Your task is to create engaging, age-appropriate stories for children.

IMPORTANT GUIDELINES:
1. Write at a level appropriate for ${readerAge}-year-old readers
2. Keep content strictly child-friendly and positive
3. Avoid any scary, violent, or inappropriate themes
4. Focus on themes of friendship, discovery, growth, and adventure
5. Use clear, engaging language with appropriate vocabulary
6. Include descriptive details and dialogue when appropriate

${formatGuideline}

${storyContext ? `\nSTORY CONTEXT:\n${storyContext}\n` : ''}

${prompt}`;

    const response = await llm.invoke([
      {
        role: "user", 
        content: basePrompt
      }
    ]);

    const generatedContentRaw = response.content.toString();
    console.log('generatedContentRaw', generatedContentRaw);

    if (!generatedContentRaw) {
      throw new Error("Gemini API failed or returned empty result");
    }

    // Post-moderation of AI output
    const outCheck = await moderateContent(generatedContentRaw, 'text');
    const generatedContent = outCheck.isAppropriate ? generatedContentRaw : "Let's imagine another story that's safe and fun for everyone!";

    console.log('generatedContent', generatedContent);

    return NextResponse.json({
      success: true,
      content: generatedContent,
      metadata: {
        type,
        readerAge,
        storyFormat,
        title,
        storyStage,
        storyTheme,
        storyGenre
      }
    });

  } catch (error) {
    console.error('Error generating story:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate story' },
      { status: 500 }
    );
  }
} 

