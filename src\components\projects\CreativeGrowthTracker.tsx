import React from "react";
import { BookOpen, Palette, Music, TrendingUp } from "lucide-react";

interface ContentStats {
    stories: number;
    artwork: number;
    music: number;
    total: number;
}

interface CreativeGrowthTrackerProps {
    contentStats: ContentStats;
}

const CreativeGrowthTracker: React.FC<CreativeGrowthTrackerProps> = ({
    contentStats
}) => {
    const trackerItems = [
        {
            icon: BookOpen,
            label: "Stories",
            count: contentStats.stories,
            color: "text-blue-600",
            bgColor: "bg-blue-100"
        },
        {
            icon: Palette,
            label: "Artwork",
            count: contentStats.artwork,
            color: "text-green-600",
            bgColor: "bg-green-100"
        },
        {
            icon: Music,
            label: "Music",
            count: contentStats.music,
            color: "text-purple-600",
            bgColor: "bg-purple-100"
        }
    ];

    return (
        <div className="bg-white border border-gray-200 rounded-lg p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-6 text-center">Creative Growth Tracker</h2>

            <div className="space-y-4">
                {trackerItems.map((item) => {
                    const Icon = item.icon;
                    return (
                        <div key={item.label} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                            <div className="flex items-center gap-3">
                                <Icon className={`h-5 w-5 ${item.color}`} />
                                <span className="text-gray-700 font-medium">{item.label}</span>
                            </div>
                            <span className="text-gray-500 font-medium">{item.count} created</span>
                        </div>
                    );
                })}

                <div className="flex items-center justify-between py-3 pt-4 border-t border-gray-200">
                    <span className="text-gray-900 font-semibold">Total Creations</span>
                    <span className="text-gray-900 font-semibold">{contentStats.total}</span>
                </div>
            </div>
        </div>
    );
};

export default CreativeGrowthTracker;
