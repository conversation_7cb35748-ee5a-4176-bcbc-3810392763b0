import { SubscriptionPlan } from './SubscriptionPlans';

export const getSubscriptionPlans = (): SubscriptionPlan[] => {
  return [
    {
      id: "monthly",
      planId: "monthly-tier",
      name: "Monthly",
      description: "Perfect for getting started with Little Spark",
      price: "$14.99",
      priceSubtext: "/month",
      tier: "monthly",
      checkoutUrl: "https://your-thrivecart-monthly-url",
      buttonText: "Subscribe Monthly",
      features: [
        "Unlimited AI creations",
        "Access to all creative tools",
        "Support via email",
      ]
    },
    {
      id: "quarterly",
      planId: "quarterly-tier",
      name: "Quarterly",
      description: "Save 15% with quarterly billing",
      price: "$35.97",
      priceSubtext: "/quarter",
      savings: "Save 15%",
      tier: "quarterly",
      checkoutUrl: "https://your-thrivecart-quarterly-url",
      buttonText: "Subscribe Quarterly",
      features: [
        "Unlimited AI creations",
        "Access to all creative tools",
        "Priority support",
        "Save 15% compared to monthly"
      ]
    },
    {
      id: "annual",
      planId: "annual-tier",
      name: "Annual",
      description: "Best value - save 25% with annual billing",
      price: "$119.99",
      priceSubtext: "/year",
      savings: "Save 25%",
      tier: "annual",
      checkoutUrl: "https://your-thrivecart-annual-url",
      buttonText: "Subscribe Annually",
      features: [
        "Unlimited AI creations",
        "Access to all creative tools",
        "Priority support",
        "Save 25% compared to monthly",
        "Bonus creative content"
      ]
    }
  ];
}; 