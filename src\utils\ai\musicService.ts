// TODO: Replace with custom backend API calls
// import { callAIService } from '../api/apiClient';

// TODO: Replace this with your custom music AI API endpoint
const callCustomMusicAI = async (params: { prompt: string; type: string }): Promise<string> => {
  // Placeholder for future custom backend integration
  try {
    console.log('[PLACEHOLDER] Would call music AI service with:', params);
    return "Here's a musical idea: Create a happy melody in C major with a bouncy rhythm, like a song about dancing in the sunshine!";
  } catch (error) {
    console.error('Error calling custom music AI service:', error);
    return "Let's make some music! What kind of song would you like to create today?";
  }
};

// TODO: Replace with custom backend for music generation
// import { supabase } from '@/lib/supabase/client';
// TODO: Re-enable when implementing custom backend security
// import { checkAIGenerationLimit } from '../security';
import { toast } from 'sonner';
import { moderateContent, handleInappropriateContent } from './contentModeration';
import { isBlocked, incrementStrike, getRemainingBlockMinutes } from './strikeTracker';

/**
 * Generate music composition description
 */
export const generateMusicComposition = async (style: string, theme: string, mood?: string): Promise<string> => {
  const moodParameter = mood ? `with a ${mood} mood` : '';
  if (isBlocked()) {
    toast.error(`Content safety: you are temporarily blocked (${getRemainingBlockMinutes()} min left).`);
    throw new Error('Blocked');
  }
  try {
    return callCustomMusicAI({
      prompt: `Generate a ${style} music composition about ${theme} ${moodParameter}`,
      type: 'music-composition',
    });
  } catch (error) {
    console.error('Error generating music composition:', error);
    throw new Error('Failed to generate music composition. Please try again.');
  }
};

/**
 * Generate actual music using Replicate's MusicGen
 */
export const generateMusic = async (params: {
  prompt: string;
  style: string;
  mood?: string;
  duration?: number;
  title?: string;
}): Promise<{
  audioUrl: string;
  generationId: string;
}> => {
  const { prompt, style, mood, duration = 10, title } = params;
  if (isBlocked()) {
    toast.error(`Content safety: you are temporarily blocked (${getRemainingBlockMinutes()} min left).`);
    throw new Error('Blocked');
  }
  
  try {
    // First validate that we have a valid prompt
    if (!prompt || prompt.trim().length < 10) {
      throw new Error('Please provide a more detailed music description');
    }

    // Check if the prompt is appropriate
    const moderation = await moderateContent(prompt, 'prompt');
    if (!moderation.isAppropriate) {
      handleInappropriateContent('music', moderation.reason);
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(moderation.reason || 'Content may not be appropriate');
    }

    console.log('[MUSIC SERVICE] Generating background music with:', { title, prompt, style, mood, duration });
    
    // Call our background music generation API
    const response = await fetch('/api/music/generate-bg', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title,
        description: prompt,
        mood,
        style,
        duration
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      if (errorData?.error) toast.warning(errorData.error);
      const strikeState = incrementStrike();
      if (strikeState.blockedUntil > Date.now()) {
        toast.error('Content safety: you are temporarily blocked for 5 minutes.');
      }
      throw new Error(errorData.error || `API request failed with status ${response.status}`);
    }

    const data = await response.json();
    
    // Handle both success and error responses that include audioUrl
    if (!data.audioUrl) {
      throw new Error(data.error || 'No audio URL received from music generation API');
    }

    // If success is false but we have audioUrl, it means we got a fallback/sample
    if (!data.success && data.error) {
      console.warn('[MUSIC SERVICE] Using fallback audio:', data.error);
      // Still return the result but with a warning
    }

    console.log('[MUSIC SERVICE] Music generation completed:', data.generationId);

    return {
      audioUrl: data.audioUrl,
      generationId: data.generationId
    };

  } catch (error) {
    console.error('Music generation error:', error);
    
    // Enhance error messaging based on error type
    if (error instanceof Error) {
      // If it's an API error with status, provide more helpful message
      if (error.message.includes('429')) {
        throw new Error('Rate limit reached. Please wait a moment before generating more music.');
      } else if (error.message.includes('401') || error.message.includes('403')) {
        throw new Error('Authentication error. Please check your API credentials.');
      } else if (error.message.includes('rate limit')) {
        throw new Error('Music generation is temporarily unavailable. Please try again in a few minutes.');
      }
      
      // Pass through the original error message
      throw error;
    }
    
    throw new Error('Failed to generate music. Please try again later.');
  }
};
